cloud_sr_config:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  inputs:
    issuelist: ""
    assignee: "{{ENV['jira_svc_acct_id']}}"
  sr_data:
    summary: "Keystore DEPLOY v{{ENV['APP_VERSION']}}-{{ENV['CI_COMMIT_SHORT_SHA']}}"
    description: "[https://dishtech.atlassian.net/wiki/spaces/ENG/pages/********/Key-Providers+and+Keystore+NFRs+and+Deployment+Steps] \n Deployment initiated from [GITLAB_BUILD_URL|{{ENV['CI_PIPELINE_URL']}}] \n Started by *{{ENV['GITLAB_USER_NAME']}}*"
    project: 
      key: "SR"
    priority:
      name: "<PERSON> (P2)"
    issuetype: 
      name: "Service Request"
    reporter:
      accountId: "{{ENV['jira_svc_acct_id']}}" 
  customfields:
    - key: "Business Justification for Change"
      value: "Keystore DEPLOY "
    - key: "Business notification required?"
      value: 
        value: "No"
    - key: "Team Name"
      value: 
        value: "dyna-mux"
    - key: "Service Component"
      value: 
        value: "dyna-drm-keystore"
    - key: "PO Approver"
      value: 
        accountId: "{{ENV['jira_svc_acct_id']}}" 

cloud_bcr_config:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  inputs:
    sr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
    assignee: "{{ENV['jira_svc_acct_id']}}"
  bcr_data:     
    project: 
      key: "BCR"
    summary: "Keystore DEPLOY v{{ENV['APP_VERSION']}}-{{ENV['CI_COMMIT_SHORT_SHA']}}" 
    description: "[https://dishtech.atlassian.net/wiki/spaces/ENG/pages/********/Key-Providers+and+Keystore+NFRs+and+Deployment+Steps] \n Deployment initiated from [GITLAB_BUILD_URL|{{ENV['CI_PIPELINE_URL']}}] \n Started by *{{ENV['GITLAB_USER_NAME']}}*"
    reporter: 
      accountId: "{{ENV['jira_svc_acct_id']}}" 
    issuetype:
      name: "Change Control"
  customfields:
    - key: "Beta Peer Approver"
      value: 
        accountId: "{{ENV['jira_svc_acct_id']}}" 
    - key: "Team Name"
      value: 
        value: "dyna-mux"
    - key: "Service Component"
      value: 
        value: "dyna-drm-keystore"
    - key: "Plan"
      value: "Keystore Version=*v{{ENV['APP_VERSION']}}*  This BCR is deployed automatically by the Keystore pipeline."
    - key: "Rollback"
      value: "Auto rollback will be done by Pipeline when deployment has failed or exceeded waiting time to check test failures"
    - key: "Testing"
      value: "testing"
    - key: "Beta Approver"
      value: 
        accountId: "{{ENV['jira_svc_acct_id']}}" 

cloud_ccr_config:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  inputs:
    sr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
    assignee: "{{ENV['jira_svc_acct_id']}}" 
  ccr_data:   
    project: 
      key: "CCR"
    summary: "Keystore DEPLOY v{{ENV['APP_VERSION']}}-{{ENV['CI_COMMIT_SHORT_SHA']}}"  
    description: "[https://dishtech.atlassian.net/wiki/spaces/ENG/pages/********/Key-Providers+and+Keystore+NFRs+and+Deployment+Steps] \n Deployment initiated from [GITLAB_BUILD_URL|{{ENV['CI_PIPELINE_URL']}}] \n Started by *{{ENV['GITLAB_USER_NAME']}}*"
    # reporter: 
    #   accountId: "{{ENV['jira_svc_acct_id']}}"
    issuetype:
      name: "Change Control"
  customfields:
    - key: "CCR Peer Approver"
      value: 
        accountId: "{{ENV['jira_svc_acct_id']}}" 
    - key: "Team Name"
      value: 
        value: "dyna-mux"
    - key: "Service Component"
      value: 
        value: "dyna-drm-keystore"
    - key: "Plan"
      value: "Keystore Version=*v{{ENV['APP_VERSION']}}*  This CCR is deployed automatically by the Keystore pipeline."
    - key: "Rollback"
      value: "Auto rollback will be done by Pipeline when deployment has failed or exceeded waiting time to check test failures"
    - key: "Testing"
      value: "testing"
    - key: "CCR Approver"
      value: 
        accountId: "{{ENV['jira_svc_acct_id']}}" 

cloud_beta_deploy_transition_sr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}" 
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  transitions:
    - id: 401
    - id: 61
    - id: 71
 
cloud_prod_deploy_transition_sr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}" 
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  transitions:
    - id: 101
    - id: 111

cloud_ready_to_execute_transition_bcr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}" 
  cr_id: "{{OUTPUTS['cloud_bcr_config']['bcr_id']}}"
  transitions:
    - id: 11
    - id: 21 
    - id: 31
 
cloud_ready_to_execute_transition_ccr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_ccr_config']['ccr_id']}}"
  transitions:
    - id: 11
    - id: 21 
    - id: 271

cloud_bcr_execute:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_bcr_config']['bcr_id']}}"
  transitions:
    - id: 71

cloud_ccr_execute:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_ccr_config']['ccr_id']}}"
  transitions:
    - id: 71

cloud_bcr_close:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_bcr_config']['bcr_id']}}"
  transitions:
    - comment: "Closing bcr as beta deployment successful"
      resolution: "Done"
      id: 61

cloud_ccr_close:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_ccr_config']['ccr_id']}}"
  transitions:
    - comment: "Closing ccr as prod deployment successful"
      resolution: "Done"
      id: 61

cloud_beta_testing_transition_sr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  transitions:
    - id: 81

cloud_warranty_transition_sr:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  transitions:
    - id: 371

bcr_sentinel_config:
  sentinel_team: "dyna-mux"
  sentinel_service: "dyna-drm-keystore"
  wait_threshold: 3
  cr_id: "{{OUTPUTS['cloud_bcr_config']['bcr_id']}}"
  url: "https://sentinel-api.cs.dishtech.org/team/"
  availability_zone: "{{ENV['cluster']}}"

ccr_sentinel_config:
  sentinel_team: "dyna-mux"
  sentinel_service: "dyna-drm-keystore"
  wait_threshold: 3
  cr_id: "{{OUTPUTS['cloud_ccr_config']['ccr_id']}}"
  url: "https://sentinel-api.cs.dishtech.org/team/"
  availability_zone: "{{ENV['cluster']}}"

cloud_sr_close_beta:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  comment: "Closing Sr due to failure in beta stage"
  resolution: "Change Failed - Rejected"

cloud_sr_close_prod:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  comment: "Closing Sr due to failure in prod stage"
  resolution: "Change Failed - Rollback"

cloud_bcr_close_failure:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_bcr_config']['bcr_id']}}"
  comment: "Closing bcr due to failure in beta stage"
  resolution: "Done"

cloud_ccr_close_failure:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_ccr_config']['ccr_id']}}"
  comment: "Closing ccr due to failure in prod stage"
  resolution: "Done"

cloud_upload_file:
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
  cr_id: "{{OUTPUTS['cloud_sr_config']['sr_id']}}"
  file_path: "{{ENV['ATTACHMENT']}}"

gchat_config:
  message: "{{ENV['message']}}"
  webhook_url: 
    - "{{ENV['WEBHOOK_ID']}}"

cloud_comment:
  comment: "{{ENV['TKT_COMMENT']}}"
  cr_id: "{{ENV['TKT_NUM']}}"
  auth:
    jiraSite: "{{ENV['jira_cloud_site']}}"
    username: "{{ENV['jira_cloud_username']}}"
    password: "{{ENV['jira_cloud_password']}}"
  assignee: "{{ENV['jira_svc_acct_id']}}"
