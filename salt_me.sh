#!/usr/bin/env bash
set -x
wget -O bootstrap-salt.sh https://github.com/saltstack/salt-bootstrap/releases/latest/download/bootstrap-salt.sh
sudo sh bootstrap-salt.sh -X -x python3
MASTER="$(python3 -c 'import subprocess; s = subprocess.check_output("sudo salt-call --local grains.get master", shell=True); print(s.split()[1])')"
if [ $MASTER == "salt" ]; then
  sudo systemctl stop salt-minion.service
  sudo systemctl disable salt-minion.service
fi
