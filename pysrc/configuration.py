#pylint: disable=C0410
import os, sys, logging, urllib

from configobj import ConfigObj

KEYSTORE_CONF_FILENAME = "keystore.conf"


class NoConfFile(Exception):
    def __str__(self):
        return 'No keystore.conf file is available'


def find_local_conf():
    """
    Used to set a fixed set of paths for an 'etc' directory containing the conf file.
    @return The full absolute path to the file
    @raies NoConfFile - if the conf file isn't found in any path.
    """
    etc_paths = ['/', os.path.abspath(sys.prefix)]  # order is important, highest to lowest priority
    for prefix in etc_paths:
        filename = os.path.join(prefix, 'etc', KEYSTORE_CONF_FILENAME)
        if os.path.isfile(filename):
            return filename
    raise NoConfFile()


class KeystoreConf(object):
    """
    This class is used to for accessing the main keystore configuration file.
    Methods are written to access various values in the file.  If a new entry
    is added, a new method should be written.
    """

    def __init__(self, conf_file=None):
        if getattr(self, 'cfg', None) is None:
            if conf_file is None:
                conf_file = find_local_conf()
            self.cfg = ConfigObj(conf_file, interpolation=False)

    @property
    def debug(self):
        if 'debug' in self.cfg['globals']:
            return self.cfg['globals'].as_bool('debug')
        return False

    @property
    def kvhls_url(self):
        """ The URL for the webservice for the Secure Media server. """
        return self.cfg['providers'].get('kvhls_url', None)

    @property
    def bands_url(self):
        """
        The URL to use for getting a list of bands from Secure Media.
        """
        if "bdremote_url" in self.cfg["providers"]:
            return self.cfg["providers"]["bdremote_url"]
        kvhls = self.kvhls_url
        if not kvhls:
            return None
        url_parts = urllib.urlparse(kvhls)
        return "http://" + url_parts.hostname + ":8083/bdremote/service?cmd=report"

    @property
    def nagra_url(self):
        """ The URL for the webservice for the Nagra server. """
        return self.cfg['providers'].get('nagra_url', None)

    @property
    def playready_url(self):
        """ The URL for the webservice for the PlayReady server. """
        return self.cfg['providers'].get('playready_url', None)

    @property
    def fairplay_url(self):
        """ The URL for the webservice for the FairPlay server. """
        return self.cfg["providers"].get("fairplay_url", None)

    @property
    def log_file(self):
        """ Used to get the location of the keystore log file. """
        return self.cfg['logs'].get('log_file', os.path.join(sys.prefix, 'var/log/keystore.log'))

    @property
    def log_level(self):
        """ Used to get the level of logging for the keystore log file. """
        log_map = {'debug': logging.DEBUG, 'info': logging.INFO,
                   'warning': logging.WARNING, 'error': logging.ERROR,
                   'critical': logging.CRITICAL}
        cfg_level = self.cfg['logs'].get('log_level', None)
        if cfg_level:
            cfg_level = cfg_level.lower()
            level = log_map.get(cfg_level, logging.WARNING)
        else:
            level = logging.WARNING
        return level

    @property
    def require_authentication(self):
        """ Used to check if authentication is required to use the keystore. """
        if 'require_auth' in self.cfg['globals']:
            return self.cfg['globals'].as_bool('require_auth')
        return False

    @property
    def slingdrm_timeout(self):
        """
            used to get a socket timeout when contacting the slingdrm server, in seconds.
            if not configured, will return 15
        """
        if "slingdrm_timeout" in self.cfg["providers"]:
            return self.cfg["providers"].as_int("slingdrm_timeout")
        return 15

    @property
    def slingdrm_url(self):
        """ url of the slingdrm server"""
        return self.cfg["providers"].get("slingdrm_url", "http://localhost:8080/metadata")

    @property
    def primetime_timeout(self):
        """
            used to get a socket timeout when contacting the primetime server, in seconds.
            if not configured, will return 15
        """
        if "primetime_timeout" in self.cfg["providers"]:
            return self.cfg["providers"].as_int("primetime_timeout")
        return 15

    @property
    def primetime_url(self):
        """ url of the primetime server"""
        return self.cfg["providers"].get("primetime_url", "http://localhost:8080/metadata")

    @property
    def primetime_policy_id(self):
        """ policy id for primetime/comcast server """
        return self.cfg["providers"].get("primetime_policy_id", "SLING-TVI-LINEAR-STAGE")

    @property
    def fairplay_timeout(self):
        """ Used to get the socket timeout when contacting the FairPlay server, in seconds. """
        if "fairplay_timeout" in self.cfg["providers"]:
            return self.cfg["providers"].as_int("fairplay_timeout")
        return 15

    @property
    def playready_timeout(self):
        """ Used to get the socket timeout when contacting the PlayReady server, in seconds. """
        if 'playready_timeout' in self.cfg['providers']:
            return self.cfg['providers'].as_int('playready_timeout')
        return 15

    @property
    def nagra_timeout(self):
        """ Used to get the socket timeout when contacting the nagra server, in seconds. """
        if 'nagra_timeout' in self.cfg['providers']:
            return self.cfg['providers'].as_int('nagra_timeout')
        return 15

    @property
    def kvhls_timeout(self):
        if 'kvhls_timeout' in self.cfg['providers']:
            return self.cfg['providers'].as_int('kvhls_timeout')
        return 2

    @property
    def widevine_timeout(self):
        if "widevine_timeout" in self.cfg["providers"]:
            return self.cfg["providers"].as_int("widevine_timeout")
        return 2

    @property
    def widevine_url(self):
        return self.cfg["providers"].get("widevine_url", None)

    @property
    def database_connection(self):
        """
        Used to get the type of connection to the database. It should be a separate subsection
        inside the 'database' section of the config file.
        """
        method = self.cfg["database"]["common"]["connection_method"]
        assert method in self.cfg["database"] and method != "common"
        return method

    @property
    def database_engine(self):
        """ Used to get the type of database being used (mysql, postgres, sqlite, etc). """
        return self.cfg['database']['common']['engine']

    @property
    def database_name(self):
        """ Used to get the name of the database"""
        return self.cfg["database"]["common"].get("name", None)

    @property
    def database_user(self):
        """ Used to get the name of the user for accessing the database. """
        return self.cfg['database']['common'].get('user', None)

    @property
    def database_password(self):
        """ Used to get the password for the database user. """
        return self.cfg['database']['common'].get('password', None)

    @property
    def database_host(self):
        """
        The name of the server hosting the database.  If the entry is empty in the
        configuration file, it will default to localhost.  It can be a name, or IP
        address.
        @return The name of the server hosting the database.
        """
        connection = self.database_connection
        host = self.cfg["database"][connection].get("host", "localhost")
        return host if host else "localhost"

    @property
    def database_port(self):
        """
        The configuration post for the database.  It can be empty, in which case its
        the default port for whatever database engine is being used.
        @return The port number for the database connection.
        """
        connection = self.database_connection
        return self.cfg["database"][connection].get("port", '')  # don't use as_int

    @property
    def connection_age(self):
        """
        Used to get the number of seconds a connection to the database should last
        before it is auto-closed.  This is the django 1.6 way of speeding up
        connections to the database by reusing them for multiple requests.  The default
        value of 0 means don't reuse the connection.  If you want the connection to
        live forever, use the value None.
        @return The number of seconds a database connection should live before being closed.
        """
        if 'connection_age' in self.cfg['database']:
            return self.cfg['database'].as_int('connection_age')
        return 0

    @property
    def segment_size(self):
        """
        Used to get the default size of a segment.  A key is only good for a specific number of
        segments, before a different key will be used.
        :return The size of a segment.
        """
        if 'segment_size' in self.cfg['globals']:
            return self.cfg['globals'].as_int('segment_size')
        return 100

    @property
    def max_range_generation(self):
        """
        Maximum number of missing keys to generate on range requests.
        :return The maximum number of missing keys to generate on range requests.
        """
        if "max_range_generation" in self.cfg["globals"]:
            return self.cfg["globals"].as_int("max_range_generation")
        return 5

    @property
    def crowd_url(self):
        """
        Used to get the url for the crowd server.
        @return The url for the crowd server.
        """
        return self.cfg['crowd'].get('url', None)

    @property
    def crowd_app(self):
        """
        Used to get the app for the crowd server.
        :return The app name for the crowd server.
        """
        return self.cfg['crowd'].get('app', None)

    @property
    def crowd_password(self):
        """
        Used to get the password for the crowd_user on the crowd server.
        @return The password for the crowd user on the crowd server.
        """
        return self.cfg['crowd'].get('password', None)

    @property
    def enable_crowd(self):
        """
        Enable crowd login. On by default. Set to false when deploying in Dany
        and other environments where we dont have access to crowd.
        @return boolean
        """
        return self.cfg['crowd'].get('enable_crowd', True)

    @property
    def mq_host(self):
        """
        Used to get the host of the message queue server.
        @return The address of the machine running the message queue.
        """
        return self.cfg['message_queue']['host']

    @property
    def mq_port(self):
        """
        Used to get the port of the message queue server.
        @return The port of the machine running the message queue.
        """
        return self.cfg['message_queue'].as_int('port')

    @property
    def mq_user(self):
        """
        Used to get the user name for accessing the virtual host on the message queue server.
        @return The user name for accessing the message queue.
        """
        return self.cfg['message_queue']['user']

    @property
    def mq_pass(self):
        """
        Used to get the password for accessing the virtual host on the message queue server.
        @return The password for accessing the message queue.
        """
        return self.cfg['message_queue']['password']

    @property
    def mq_virtual(self):
        """
        Used to get the name of the virtual host to use on the message queue server.
        @return The name of the virtual host on the message queue server.
        """
        return self.cfg['message_queue']['virtual_host']

    @property
    def uwsgi_socket(self):
        """
        Used to get the network address which nginx uses to contact the django server
        """
        result = "unix:///run/keystore/keystore.sock"
        if "uwsgi_socket" in self.cfg["globals"]:
            result = self.cfg["globals"]["uwsgi_socket"]
        return result

    @property
    def use_dynatrace(self):
        """
        Set true when dynatrace is enabled
        @return True when dynatrace id configured.
        """
        return self.cfg['monitoring'].get('use_dynatrace', False)

    @property
    def use_newrelic(self):
        """
        Set true when newrelic is enabled
        @return True when newrelic id configured.
        """
        return self.cfg['monitoring'].get('use_newrelic', False)