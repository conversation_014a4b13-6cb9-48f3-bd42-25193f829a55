import uuid
import re
import operator
from functools import reduce
from django.contrib import admin
from django.db import models
from django.contrib.admin.utils import lookup_needs_distinct
from django.core.exceptions import PermissionDenied

class ReadOnlyAdmin(admin.ModelAdmin):
    """
    Admin class that overrides default permissions to create a readonly user behavior.
    """
    def get_search_results(self, request, queryset, search_term):
        """
        Return a tuple containing a queryset to implement the search
        and a boolean indicating if the results may contain duplicates.
        """
        # Apply keyword searches.
        def construct_search(field_name):
            if field_name.startswith('^'):
                return "%s__istartswith" % field_name[1:]
            elif field_name.startswith('='):
                return "%s__iexact" % field_name[1:]
            elif field_name.startswith('@'):
                return "%s__search" % field_name[1:]
            else:
                return "%s__icontains" % field_name

        use_distinct = False
        search_fields = self.get_search_fields(request)
        if search_fields and search_term:
            orm_lookups = [construct_search(str(search_field))
                           for search_field in search_fields]
            for bit in search_term.split():
                _match = re.match(r"^uuid:\s*(.+)", bit.strip(), re.IGNORECASE)
                if _match:
                    try:
                        bit = str(uuid.UUID(_match.group(1)))
                    except ValueError:
                        pass
                or_queries = [models.Q(**{orm_lookup: bit})
                              for orm_lookup in orm_lookups]
                queryset = queryset.filter(reduce(operator.or_, or_queries))
            if not use_distinct:
                for search_spec in orm_lookups:
                    if lookup_needs_distinct(self.opts, search_spec):
                        use_distinct = True
                        break
        return queryset, use_distinct

    def has_add_permission(self, request):
        if self._user_is_readonly(request):
            return False
        return super(ReadOnlyAdmin, self).has_add_permission(request)

    def has_delete_permission(self, request, obj=None):
        if self._user_is_readonly(request):
            return False
        return super(ReadOnlyAdmin, self).has_delete_permission(request, obj)

    def get_actions(self, request):
        if self._user_is_readonly(request):
            return []
        return super(ReadOnlyAdmin, self).get_actions(request)

    def get_form(self, request, *args, **kwargs):
        """
        Used to give the form created access to the 'user' that created the form.
        """
        form = super(ReadOnlyAdmin, self).get_form(request, *args, **kwargs)
        form.current_user = request.user
        if self._user_is_readonly(request):
            for field in form.base_fields.values():
                field.disabled = True
        else:
            for field in form.base_fields.values():
                field.disabled = False
        return form

    def get_inline_instances(self, request, obj=None):
        """
        Used to get instances of the inline classes for this admin UI component.  It checks if the user
        has readonly, or full access, and gets the appropriate attribute.  If neither attribute exists,
        it just uses the parent's version.
        """
        # pylint: disable=no-member
        result = []
        if self._user_is_readonly(request) and hasattr(self, "readonly_inlines"):
            for inline_class in self.readonly_inlines:
                result.append(inline_class(self.model, self.admin_site))
        elif hasattr(self, "regular_inlines"):
            for inline_class in self.regular_inlines:
                result.append(inline_class(self.model, self.admin_site))
        else:
            result = super(ReadOnlyAdmin, self).get_inline_instances(request, obj)
        return result

    def change_view(self, request, object_id, form_url="", extra_context=None):
        try:
            return super(ReadOnlyAdmin, self).change_view(request, object_id, extra_context=extra_context)
        except PermissionDenied:
            pass
        if request.method == "POST":
            raise PermissionDenied
        request.readonly = True
        return super(ReadOnlyAdmin, self).change_view(request, object_id, extra_context=extra_context)

    def get_list_display_links(self, request, list_display):
        # if you have readonly inlines, you probably want the display link
        if self._user_is_readonly(request) and not hasattr(self, "readonly_inlines"):
            return None
        return super(ReadOnlyAdmin, self).get_list_display_links(request, list_display)

    def _user_is_readonly(self, request):
        groups = [x.name for x in request.user.groups.all()]
        return "readonly" in groups

    def save_model(self, request, obj, form, change):
        if self._user_is_readonly(request):
            raise PermissionDenied
        else:
            super(ReadOnlyAdmin, self).save_model(request, obj, form, change)
