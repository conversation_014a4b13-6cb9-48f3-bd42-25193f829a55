# django packages
from django import forms
from django.contrib import admin
from django.core.exceptions import ValidationError
from django.forms.models import BaseInlineFormSet
from django.utils.html import format_html

# Keystore packages
from pysrc.keystore_app.models import KeyConfig, KeyConfigRelation, SecureMediaProvider, NagraProvider
from pysrc.keystore_app.models.providers import Provider
from .read_only import ReadOnlyAdmin

REGULAR_FILTER = "admin/filter_listing.html"
SIDEBAR_FILTER = "admin/change_list_filter_sidebar.html"


class KeyConfigRelationFormSet(BaseInlineFormSet):
    """
    This class represents the group/set of inline KeyConfigRelation objects at the bottom of the KeyConfig
    editing page.  It is used to ensure that the KeyConfig's selected band ID is in fact coming from the
    SecureMediaProvider selected.
    """

    def clean(self):
        """
        Called automatically as part of the KeyConfig's form validation.  It serves 3 main purposes.  It ensures that
        we don't have too many instances of a particular provider associated with the KeyConfig (controlled by the
        ALLOWED_INSTANCES field in Provider hierarchy).  It also ensures that if there is a SecureMedia provider,
        the selected band_id is offered by that selected SecureMediaProvider.  A database contraint ensure that the
        same instance is only associated with the KeyConfig once, even if ALLOWED_INSTANCES is more than 1.
        """
        super(KeyConfigRelationFormSet, self).clean()
        instance_count = {}  # keep a count of each instance type, the "key" is the "get_type" of the provider
        band_id = self.instance.band_id

        for f in self.forms:
            provider = f.instance.content_object
            if provider is None:
                continue
            if provider.ALLOWED_INSTANCES > 0:
                provider_type = provider.get_type()
                if provider_type not in instance_count:
                    instance_count[provider_type] = 1
                elif instance_count[provider_type] == provider.ALLOWED_INSTANCES:
                    raise ValidationError("Only %i %s provider(s) are allowed" % (
                        provider.ALLOWED_INSTANCES, provider_type))
                else:
                    instance_count[provider_type] += 1
            # band IDs are only used for live keys
            if isinstance(provider, SecureMediaProvider) and self.instance.is_live():
                bands = provider.update_band_names()
                if band_id not in bands:
                    raise ValidationError("The selected band ID isn't offered by the selected SecureMedia provider")


class KeyConfigRelationInline(admin.TabularInline):
    model = KeyConfigRelation
    verbose_name = "Key Config DRM Relation"
    verbose_name_plural = "Key Config DRM Relations"
    formset = KeyConfigRelationFormSet
    related_lookup_fields = {
        'generic': [['content_type', 'object_id']],
    }

    def get_extra(self, request, obj=None):
        """
        Used to determine how many 'extra' relations to show on the screen.  If we are creating a new KeyConfig we give
        a few more than the maximum number of unique provider clases (incase multiple insteances are needed).  If we
        are editing an existing one, give 2 more, otherwise none.
        @param obj - The existing KeyConfig, None if creating a new KeyConfig.
        """
        if obj is None:
            return len(Provider.child_classes_query_set()) + 2
        elif obj.usage == KeyConfig.VOD_KEY or obj.key_set.count() == 0:
            return 2
        return 0  # no extra ones

    def get_max_num(self, request, obj=None):
        """
        Used to determine the maximum number of keyconfig rellations that should appear inline.  If there
        is already a KeyConfig, the number is set to the existing providers, so you don't get the '+' sign
        to ad more.  Returning a value of None means there is no limit.  Realize this is only a visual
        maximum, it doesn't refer to a database restriction.
        @param obj - The existing KeyConfig, None if creating a new KeyConfig.
        @return The maximum number of relations to show.
        """
        if obj is not None and obj.is_live() and obj.key_set.count() > 0:
            return obj.num_providers()
        return None

    def get_readonly_fields(self, request, obj=None):
        """
        Method used to make all the providers settings read-only when editing an existing KeyConfig.
        @param obj - The existing KeyConfig, None if creating a new KeyConfig.
        @return A list of read-only fields for the supplied KeyConfig.
        """
        if obj is not None and obj.is_live() and obj.key_set.count() > 0:
            field_names = [x.name for x in self.model._meta.get_fields()]
            field_names.remove("id")
            field_names.remove("content_object")
            return field_names
        return []

    def has_delete_permission(self, request, obj=None):
        """
        Method used to prevent providers from being deleted from an existing KeyConfig.
        @param obj - The existing KeyConfig, None if creating a new KeyConfig.
        """
        if obj is not None and obj.is_live():
            return obj.key_set.count() == 0
        return True


class KeyConfigForm(forms.ModelForm):
    band_id = forms.ChoiceField(required=False)

    class Meta(object):
        model = KeyConfig
        # pylint: disable=modelform-uses-exclude
        exclude = []
        widgets = {'usage': forms.RadioSelect, 'rotation_method': forms.RadioSelect}

    # pylint: disable=E1101
    def __init__(self, *args, **kwargs):
        """
        Constructor used to load the valid "choices" for band names.  Normally you don't override
        the constructor on a django form, but this is the suggested method to dynamically load a
        pull-down menu.
        """
        super(KeyConfigForm, self).__init__(*args, **kwargs)
        bands = SecureMediaProvider.get_all_bands()
        if not bands:
            if SecureMediaProvider.objects.count() > 0:
                self.fields["band_id"].choices = [("Error", "Problem getting bands from SM server")]
            else:
                self.fields["band_id"].choices = [("Error", "No configured SecureMedia providers")]
        else:
            self.fields["band_id"].choices = []
            for b in bands:
                entry = (b, "%s (used by: %s)" % (b, ", ".join(bands[b])))
                self.fields["band_id"].choices.append(entry)

    def clean(self):
        """
        Custom clean method to ensure that if the instance is for live or user content, a valid
        key lifetime has been supplied. It also ensure that if a the config is for live content,
        a band ID has been entered.  This is only done here instead of a custom 'field clean'
        because this validation depends on multiple fields.
        """
        cleaned_data = super(KeyConfigForm, self).clean()
        usage = cleaned_data.get('usage', self.instance.usage)
        lifetime = cleaned_data.get('lifetime', self.instance.lifetime)

        # check valid lifetimes
        if usage == KeyConfig.LIVE_KEY and lifetime is None:
            usage_name = [name[1] for name in KeyConfig.KEY_CONFIG_USAGE_CHOICES if name[0] == usage][0]
            raise ValidationError("'%s' Key Configs require a key lifetime" % usage_name)

        # force particular settings for VOD KeysConfigs
        if usage == KeyConfig.VOD_KEY:
            cleaned_data["rotation_method"] = KeyConfig.SEGMENT_ROT
            cleaned_data["seg_size"] = 1800 * 24  # 1 full day worth of segments
            cleaned_data["lifetime"] = None
            cleaned_data["pre_create_keys"] = 0  # no precache for vod, just to be safe
            cleaned_data["band_id"] = "------"
        elif lifetime >= 12:  # force no pre-cached keys if lifetime is more than 12 hours
            cleaned_data["pre_create_keys"] = 0
        return cleaned_data


class KeyConfigAdmin(ReadOnlyAdmin):
    change_list_filter_template = REGULAR_FILTER
    change_form_template = "key_config.html"
    list_display = ('name', 'band_id', 'rotation_method', 'seg_size', 'rotation_time', 'usage', 'lifetime',
            'pre_create_keys', 'provider_list', "notes")
    list_filter = ['seg_size', 'usage', 'rotation_method']
    search_fields = ["name"]
    inlines = [KeyConfigRelationInline]

    def get_readonly_fields(self, request, obj=None):
        """
        Used to designate certain fields as 'write-once'.  All the fields can be edited when a config is being
        created, but once it is created (obj != None), then certain fields should be locked, based on the
        type of the key config.  For live KeyConfigs, if there are keys associated with the KeyConfig, only a few
        fields are editable.  For vod, a few more are editable (but not all).
        same restricts as editing a vod key.
        :param request - The HttpRequest from the webpage.
        :param obj - The object being edited, or None if being created.
        :return A list of field names that should be deemed read-only.
        """
        if obj is not None:
            all_fields = [x.name for x in self.model._meta.get_fields() if not x.auto_created]
            all_fields.remove("notes")  # can always edit the notes

            if obj.usage == KeyConfig.LIVE_KEY and obj.key_set.count() > 0:  # live with keys
                # allow editing the fields below
                all_fields.remove("lifetime")
                all_fields.remove("pre_create_keys")
                return all_fields
            elif obj.usage == KeyConfig.VOD_KEY:
                return all_fields
        return []  # everything editable

    def get_form(self, request, obj=None, **kwargs):
        """
        Used to get a custom form when needed.  If the object doesn't exist, or it has no keys associated with it,
        use our custom KeyConfigForm, otherwise use the default django one.  The reason of this is there is a
        bug in django when using a read-only choice field that causes the field to appear twice, so we only use
        out form if you are allowed to edit the 'band_id'.
        :param request - The HttpRequest from the webpage.
        :param obj - The object being edited, or None if being created.
        :return The form to use for creating/editing the KeyConfig
        """
        if obj is None or obj.key_set.count() == 0:
            kwargs["form"] = KeyConfigForm
        return super(KeyConfigAdmin, self).get_form(request, obj, **kwargs)

    def provider_list(self, obj):
        """
        Used to get a list of all the providers this KeyConfig is using.
        """
        bullet_list = '<ul>'
        for rel in obj.keyconfigrelation_set.all().order_by("object_id"):  # sorts by provider name
            prov = rel.content_object
            if prov:
                bullet_list += '<li><a href="../%s/?q=%s">%s</a></li>' % (prov._meta.model_name, prov.name, prov.name)
            else:
                bullet_list += '<li>Error!</li>'
        return format_html(bullet_list + '</ul>')
    provider_list.short_description = 'Providers'
    provider_list.allow_tags = True


# Register your models here.
admin.site.register(KeyConfig, KeyConfigAdmin)
