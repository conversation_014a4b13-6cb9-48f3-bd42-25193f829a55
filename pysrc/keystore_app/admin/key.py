# pylint: disable=W0613,W0614
# django packages
from django.contrib import admin
from django.utils.html import format_html

# Keystore packages
from pysrc.keystore_app.models import Key, KeyUsage

REGULAR_FILTER = "admin/filter_listing.html"
SIDEBAR_FILTER = "admin/change_list_filter_sidebar.html"


class KeyAdmin(admin.ModelAdmin):
    change_list_filter_template = REGULAR_FILTER
    list_display = ["id", "created", "accessed", "expires", "algorithm", "config_link", "has_all_meta",
            "provider_names"]
    list_filter = ["created", "algorithm", "config", "has_all_meta"]
    search_fields = ["id"]
    list_display_links = None  # so you can't edit a key

    def has_add_permission(self, request):
        """ Used to prevent the 'Add Key' button from appearing on the admin site """
        return False

    def config_link(self, obj):
        return format_html('<a href="../keyconfig/?q=%s">%s</a>' % (obj.config.pk, obj.config.pk))
    config_link.short_description = "Key Config"
    config_link.allow_tags = True

    def provider_names(self, obj):
        return [str(provConfig.name) for provConfig in obj.config.all_providers()]
    provider_names.short_description = "Provider Names"


class KeyUsageAdmin(admin.ModelAdmin):
    list_display = ["key_link", "stream_link", "startSeg", "end"]
    search_fields = ["key__id", "stream__id"]

    def has_add_permission(self, request):
        """ Used to prevent the 'Add Key' button from appearing on the admin site """
        return False

    def key_link(self, obj):
        """
        Used to make the key's ID an active link that will goto a page with the
        full key info.
        """
        return format_html('<a href="../key/?q=%s">%s</a>' % (obj.key_id, obj.key_id))

    def stream_link(self, obj):
        """
        Used to make the stream's ID an active link that will goto a page with the
        full stream info.
        """
        return format_html('<a href="../stream/?q=%s">%s</a>' % (obj.stream_id, obj.stream_id))

    def end(self, obj):
        """ If the key isn't live, it's end segment is infinity. """
        return obj.endSeg if obj.key.config.is_live() else "Infinity"

    # custom column titles
    key_link.short_description = "Key ID"
    stream_link.short_description = "Stream ID"
    end.short_description = "End Segment"
    # indicate that a function will send back HTML, not raw text
    key_link.allow_tags = True
    stream_link.allow_tags = True


# Register your models here.
admin.site.register(Key, KeyAdmin)
admin.site.register(KeyUsage, KeyUsageAdmin)
