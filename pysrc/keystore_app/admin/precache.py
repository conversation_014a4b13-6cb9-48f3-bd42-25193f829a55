# django packages
from django.contrib import admin
from django.utils.html import format_html

# Keystore packages
from pysrc.keystore_app.models import PreCachedKeys, Key


class PreCachedKeysAdmin(admin.ModelAdmin):
    list_display = ["id", "content_id", "keyconfig_link", "cached_keys"]
    search_fields = ["content_id"]
    list_filter = ["key_config"]
    list_display_links = None  # so you can't edit a PreCachedKeys object

    def has_add_permission(self, request):
        """
        Used to prevent the 'Add Key' button from appearing on the admin site.
        """
        return False

    def keyconfig_link(self, obj):
        """
        Used to create a link to the KeyConfig associated with this PreCachedKeys objects.
        @param obj - The PreCachedKeys object.
        """
        return format_html('<a href="../keyconfig/?q=%s">%s</a>' % (obj.key_config.name, obj.key_config.name))
    keyconfig_link.short_description = "Key Config"
    keyconfig_link.allow_tags = True

    def cached_keys(self, obj):
        bullet_list = "<ul>"
        for cached_key in Key.objects.filter(cached_by=obj):
            key_id_str = str(cached_key.id)
            bullet_list += '<li><a href="../key/?q=%s">%s</a>' % (key_id_str, key_id_str)
        return format_html(bullet_list + "</ul>")
    cached_keys.short_description = "Cached Keys"
    cached_keys.allow_tags = True


# Register your models here.
admin.site.register(PreCachedKeys, PreCachedKeysAdmin)
