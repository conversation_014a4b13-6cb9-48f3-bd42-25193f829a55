# django packages
from django.contrib import admin
from django.utils.html import format_html

# Keystore packages
from pysrc.keystore_app.models import Stream
from django.db.models import Count


class ActiveStreamFilter(admin.SimpleListFilter):
    """
    This class is a custom filter for Stream objects, where we want to filter to see just active/inactive
    streams.  A custom filter is needed because "num_used_keys" on a stream is a python property, not a django
    field, and regular django filtering only works on fields because it generates an SQL query/filter for
    the database to perform.  And the database obviously knows nothing about python properties.
    """
    # 2 fields required by parent class
    title = "Encryption Keys"
    parameter_name = ""

    def lookups(self, _request, _model_admin):
        """
        Method used by django to get a list of tuples for all the various filters for this setting.  The
        first field in the tuple is the 'value' to use, and the second is the name/label presented to the
        user on the admin webpage.
        @param _request - The web request, not used.
        @param _model_admin - The admin model where the request came from, not used.
        @return A list of tuples for the filter setting and label.
        """
        return [(True, "With Keys"), (False, "No Keys")]

    def queryset(self, _request, queryset):
        """
        Method used to take a query set and remove all the streams that are have keys, or no keys, based on the
        filter's settings.
        @param _request - The web request, unused.
        @param queryset - The queryset that needs to be filtered on.
        @return A clone of the queryset param, with the appropriate streams removed.
        """
        if self.value() is None:  # the default 'all', meaning no filtering
            return queryset

        if self.value() == 'True':
            queryset = queryset.annotate(keys=Count('keyusage')).filter(keys__gt=0)
        elif self.value() == 'False':
            queryset = queryset.annotate(keys=Count('keyusage')).filter(keys=0)

        return queryset


class StreamAdmin(admin.ModelAdmin):
    list_filter = [ActiveStreamFilter]
    list_display = ('id', 'content_id', 'channel', 'encryption_block_size', 'using_keys')
    search_fields = ['id', "content_id"]
    readonly_fields = ('id', 'content_id', 'encryption_block_size')

    def has_add_permission(self, request):
        """ Used to prevent the 'Add Key' button from appearing on the admin site """
        return False

    def using_keys(self, obj):
        """ Used to get a list of all the keys this stream is using. """
        bullet_list = '<ul>'
        for result in obj.keyusage_set.all().order_by('startSeg'):
            bullet_list += '<li><a href="../key/?q=%s">%s</a> : segments %s-%s' % (
                str(result.key_id), str(result.key_id), result.startSeg, result.endSeg)
        return format_html(bullet_list + '</ul>')
    using_keys.allow_tags = True



# Register your models here.
admin.site.register(Stream, StreamAdmin)
