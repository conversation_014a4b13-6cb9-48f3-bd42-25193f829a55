# django packages
from django.contrib import admin
from django.http import HttpResponse
from django.utils.html import format_html

# System packages
import json

# Keystore packages
from pysrc.keystore_app.models import SecureMediaProvider, NagraProvider, InternalProvider, \
        PlayReadyProvider, WidevineProvider, FairPlayProvider, PrimetimeProvider, SlingDrmProvider
from pysrc.keystore_app.views import JSON_TYPE
from .read_only import ReadOnlyAdmin
from pysrc.configuration import KeystoreConf
keystoreConf = KeystoreConf()


class ProviderAdmin(ReadOnlyAdmin):
    list_display = ['name', 'url', 'default_url', 'config_link']
    search_fields = ('name', )
    actions = ['check_connection']

    def default_url(self, obj):
        if obj:
            provider = (type(obj).__name__).lower().split('provider')[0]
            url = getattr(keystoreConf, '{}_url'.format(provider)) if provider != 'internal' else 'Internal'
            return format_html("</ul>{}</ul>".format(url))
    default_url.short_description = "URL(default)"
    default_url.allow_tags = True

    def get_readonly_fields(self, _request, obj=None):
        """
        Used to designate certain fields as 'write-once'.  All the fields can be edited when a provider
        is being created, but once it is created (obj != None), then all fields should be locked.
        :param request - The HttpRequest from the webpage.
        :param obj - The object being edited, or None if being created.
        :return A list of field names that should be deemed read-only.
        """
        if obj:  # we are editing an existing config, these are read-only fields now
            return [field.name for field in self.model._meta.fields]
        return []

    def config_link(self, obj):
        """
        Used to make the key config's ID an active link that will goto a page with the full key config info.
        """
        from pysrc.keystore_app.models import KeyConfigRelation
        bullet_list = '<ul>'
        for rel in KeyConfigRelation.objects.all():
            if rel.content_object == obj:
                bullet_list += '<li><a href="../keyconfig/?q=%s">%s</a>' % (rel.key_config.pk, rel.key_config.pk)
        return format_html(bullet_list + '</ul>')
    config_link.short_description = 'Key Configs'
    config_link.allow_tags = True

    def check_connection(self, _request, queryset):
        """
        This method is the action that appears on the form used to check the
        connection to the provider's server.
        """
        if len(queryset) != 1:
            return HttpResponse("Please select only 1 provider.")
        try:
            url = queryset[0].get_url()
            if queryset[0].check_connection(url=url):
                result = {"status": "OK", "healthy": True}
                return HttpResponse(json.dumps(result), content_type=JSON_TYPE)
        except Exception as ex:  # pylint: disable=W0703
            result = {"status": "FAILED", "healthy": False, "message": str(ex)}
            response = HttpResponse(json.dumps(result), content_type=JSON_TYPE)
            response.status_code = 503
            return response

        # the check failed, but didn't throw an exception
        result = {"status": "FAILED", "healthy": False,
                  "message": "Check of provider connection failed."}
        response = HttpResponse(json.dumps(result), content_type=JSON_TYPE)
        response.status_code = 417
        return response
    check_connection.short_description = "Check Connection to selected provider"


class SecureMediaAdmin(ProviderAdmin):
    list_display = ProviderAdmin.list_display + ["bands_url", "default_bands_url", "band_ids"]

    def default_url(self, _):
        return format_html("</ul>{}</ul>".format(keystoreConf.kvhls_url))
    default_url.short_description = "URL (kvhls)(default)"
    default_url.allow_tags = True

    def default_bands_url(self, _):
        return format_html("</ul>{}</ul>".format(keystoreConf.bands_url))
    default_bands_url.short_description = "URL (bdremote)(default)"
    default_bands_url.allow_tags = True

    def band_ids(self, obj):
        bullet_list = "<ul>"
        for band in obj.update_band_names():
            bullet_list += "<li>%s" % band
        return format_html(bullet_list + "</ul>")
    band_ids.short_description = "Band IDs"
    band_ids.allow_tags = True

    def get_readonly_fields(self, request, obj=None):
        """
        Used to ensure that the 'bands_url' is always editable.
        :param request - The HttpRequest from the webpage.
        :param obj - The object being edited, or None if being created.
        :return A list of field names that should be deemed read-only.
        """
        result = super(SecureMediaAdmin, self).get_readonly_fields(request, obj)
        if obj:
            result.remove("bands_url")
        return result


class WidevineAdmin(ProviderAdmin):

    list_display = ["name", "provider", 'url', 'default_url', 'config_link']

    def default_url(self, _):
        return format_html("</ul>{}</ul>".format(keystoreConf.widevine_url))
    default_url.short_description = "Proxy URL(default)"
    default_url.allow_tags = True

    def get_readonly_fields(self, request, obj=None):
        """
        Used to ensure that the 'provider' is always editable.
        :param request - The HttpRequest from the webpage.
        :param obj - The object being edited, or None if being created.
        :return A list of field names that should be deemed read-only.
        """
        result = super(WidevineAdmin, self).get_readonly_fields(request, obj)
        if obj:
            result.remove("provider")
        return result

class PrimetimeAdmin(ProviderAdmin):
    list_display = ProviderAdmin.list_display + ["primetime_policy_id"]

    def get_readonly_fields(self, request, obj=None):
        result = super(PrimetimeAdmin, self).get_readonly_fields(request, obj)
        if obj:
            result.remove("primetime_policy_id")
        return result


# Register your models here.
admin.site.register(SecureMediaProvider, SecureMediaAdmin)
admin.site.register(NagraProvider, ProviderAdmin)
admin.site.register(InternalProvider, ProviderAdmin)
admin.site.register(PlayReadyProvider, ProviderAdmin)
admin.site.register(WidevineProvider, WidevineAdmin)
admin.site.register(FairPlayProvider, ProviderAdmin)
admin.site.register(PrimetimeProvider, PrimetimeAdmin)
admin.site.register(SlingDrmProvider, ProviderAdmin)
