from django.contrib import admin
from django.utils.html import format_html
from django.contrib.auth.models import Group, User
from django.contrib.auth.admin import UserAdmin, GroupAdmin
from django_celery_results.models import TaskResult
from django_celery_results.admin import TaskResultAdmin
from .read_only import ReadOnlyAdmin


class Mod_UserAdmin(ReadOnlyAdmin, UserAdmin):

    list_display = ["username", "email", "first_name", "last_name", "is_staff", "is_superuser", "is_active",
                    "show_groups", "show_permissions", "date_joined", "last_login"]

    def show_groups(self, obj):
        result = ""
        for grp in obj.groups.all():
            result += "<p>%s</p>" % grp.name
        return format_html(result)
    show_groups.short_description = "Groups"

    def show_permissions(self, obj):
        result = ""
        for permission in obj.user_permissions.all():
            result += "<p>%s</p>" % permission.name
        return format_html(result)
    show_permissions.short_description = "Permissions"


class Mod_GroupAdmin(ReadOnlyAdmin, GroupAdmin):
    pass


class Mod_TaskResultAdmin(ReadOnlyAdmin, TaskResultAdmin):
    pass

admin.site.unregister(User)
admin.site.unregister(Group)
admin.site.unregister(TaskResult)
admin.site.register(User, Mod_UserAdmin)
admin.site.register(Group, Mod_GroupAdmin)
admin.site.register(TaskResult, Mod_TaskResultAdmin)
