# pylint: skip-file
# System
import base64
import time
import os
import uuid
from urllib.error import URLError
import socket

# 3rd party
from flufl.enum import IntEnum, Enum
from suds import client

# keystore_app package
from ... import settings, logger
from ..providers import ProviderException, Provider, pre_delete_handler

KEY_AND_SIGNALIZATION_V1_WSDL_PATH = "file://" + os.path.join(
    os.path.dirname(os.path.abspath(__file__)), "KeyAndSignalizationServiceV1.wsdl")


class NagraException(ProviderException):
    """ Empty class for Nagra specific exceptions. """
    pass


class EncryptionMethodIndicator(IntEnum):
    AES_128_CBC = 0x4023
    """
    For AES-128 in CBC mode. Apple Live Streaming.  This is a value that <PERSON><PERSON><PERSON> of Nagra gave to me.
    Other possible values from the Nagra docs are AES-ECB, TDES, DVB-CSA?
    """


class StreamingMode(Enum):
    """ Simple enum for the various streaming modes for Nagra. """
    DASH = "DASH"
    HLS = "HLS"
    PIFF = "PIFF"
    CUSTOM = "CUSTOM"


class NagraProvider(Provider):
    """
    NagraProvider provides a mechanism for interacting with Nagra's
    KeyAndSignalizationServiceV1 service.
    """
    VERSION = "1.0"
    ALLOWED_INSTANCES = 0  # zero means unlimited

    #key_uri = models.URLField(editable=False)
    #drm_sys_id = models.CharField(max_length=50, editable=False)
    #drm_name = models.CharField(max_length=50, editable=False)

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'

    def push_to_provider(self, key, content_id):
        """
        Method used to push this key to the designated Nagra provider.  If works this objects provider meta-data
        is updated.  If it fails, it is logged.
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        try:
            # get_key either retrieves an existing key, or sets a new one
            nag_key = self.get_key(key.config.is_live(), content_id, key.guid, key.secret)
            # validate keys match
            if base64.b64encode(key.secret).decode() == nag_key:
                key.providers_meta.append(self.to_dict())
                key.save()
            else:
                logger.warning("Key returned by Nagra server didn't match key passed in for keyID=%s", self.id)
        except NagraException as ex:
            logger.exception("Problem talking to Nagra server for key config %s: %s", self.name, ex)

    @staticmethod
    def check_connection(use_timeout=settings.nagra_timeout, url=settings.nagra_url):
        """
        Method used to check the network connection to the provider.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.  It will use the settings
            default value if none is supplied.
        """
        try:
            _client = client.Client(KEY_AND_SIGNALIZATION_V1_WSDL_PATH, location=url)
            _client.set_options(timeout=use_timeout)
            resp = _client.service.heartbeat(version=NagraProvider.VERSION)
        except (socket.timeout, URLError) as ex:
            raise NagraException('problem contacting Nagra server for keyconfig %s: %s' % (self.name, str(ex)))
        if hasattr(resp, 'errorMessage'):
            raise NagraException(resp.errorMessage)
        if resp.returnCode != "OK":  # incase of version problems
            raise NagraException(resp.returnCode)
        if resp.status != "ACTIVE":
            raise NagraException("Nagra servers are in a STANDBY state")
        return True

    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        return {
            'type': self.get_type(),
            'name': self.name,
            #'state': self.state,
            'drm_sys_id': self.drm_sys_id,
            'drm_name': self.drm_name,
            'key_uri': self.key_uri,
        }

    @staticmethod
    def fix_uri(uri):
        """
        Support method to properly format the result from the Nagra service.
        The PRM is base64 encoded... The KSS service doesn't pad, but the PAK needs
        it padded on 4 byte boundaries.
        :param uri - the keyUri that came from the Nagra service.
        """
        return uri + ('=' * (4 - (len(uri) - (uri.rindex('=') + 1)) % 4))

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom,
        or the value in the config file.
        :return The url for contacting this provider.
        """
        if self.url is None:
            return settings.nagra_url
        if len(self.url) != 0:
            return self.url
        return settings.nagra_url

    def get_type(self):
        """ Method used to get the type of this providers as a string. """
        return 'nagra'

    def get_key(self, is_live, content_id, key_id, key, streaming_mode=StreamingMode.HLS,
                emi=EncryptionMethodIndicator.AES_128_CBC):
        """
        Retrieve the key or add a new one.
        :param is_live - Whether the keyconfig is for a live stream or not.
        :param content_id - the content ID to send to the Nagra server, can't be empty.
        :param key_id - the UUID key_id to be sent to the Nagra server
        :param key - the actual key to send to the Nagra server
        :param StreamingMode streaming_mode: default HLS
        :param EncryptionMethodIndicator emi: default AES_128_CBC
        """
        # check params
        if not isinstance(key_id, uuid.UUID):
            raise NagraException('invalid key_id')
        if content_id is None:
            raise NagraException("contentID can't be empty")
        distribution_mode = "LIVE" if is_live else "VOD"
        retry_count = 1

        while True:
            try:
                nagra_url = self.get_url()
                _client = client.Client(KEY_AND_SIGNALIZATION_V1_WSDL_PATH, location=nagra_url)
                _client.set_options(timeout=settings.nagra_timeout)
                resp = _client.service.getKeyAndSignalization(
                    # always supply a key, either generated by SecureMedia, or the Keystore itself.
                    # when supplied, Nagra won't generate a key
                    scheduledKey={
                        "time": int(time.time()),  # time when key should become active, now
                        "contentKey": {
                            "keyId": key_id,
                            "key": base64.b64encode(key).decode(),
                        }
                    },
                    drmContent={
                        "drmContentId": content_id,
                        "profile": {
                            "distributionMode": distribution_mode,
                            "streamingMode": streaming_mode.value,  # currently will always be HLS
                            "emi": emi.value,
                            #"cryptoPeriod": int(foo) - this is for future use defined by Nagra
                        }
                    })
            except (socket.timeout, URLError) as ex:
                raise NagraException("problem contacting server %s : %s" % (nagra_url, ex))
            except Exception as ex:
                raise NagraException("some other weird exception occurred contacting server %s : %s" % (nagra_url, ex))

            if resp.status == "OK":
                break
            else:
                # a server error, retry the request with a slight delay
                retry_count += 1
                if resp.status == "INTERNAL_ERROR" and retry_count < 5:
                    time.sleep(0.2)
                    logger.warning("Got INTERNAL_ERROR from Nagra, retrying request, count=%i", retry_count)
                    continue
                elif retry_count > 5:
                    logger.warning("retry limit reached in getting key from nagra")
                    raise NagraException("retry limit reached in getting key from Nagra server %s" % nagra_url)
                else:
                    logger.warning("unknown error when getting nagra key from %s. raising exception" % nagra_url)
                    # different problem, throw an error
                    error = {'status': resp.status}
                    if hasattr(resp, 'errorMessage'):
                        error['message'] = resp.errorMessage
                    raise NagraException(error)

        # results are good, save them in instance variables
        self.key_uri = resp.drmSignalization.hls[0].keyUri
        #self.key_uri = NagraProvider.fix_uri(resp.drmSignalization.hls[0].keyUri)
        self.drm_sys_id = resp.drmSignalization.hls[0].drmSystemId
        self.drm_name = resp.drmSignalization.hls[0].drmName
        self.save()

        return resp.contentKey.key


# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=NagraProvider)
