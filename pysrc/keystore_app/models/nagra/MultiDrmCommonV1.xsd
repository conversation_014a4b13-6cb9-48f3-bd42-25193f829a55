<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by Administrator (Nagravision SA) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:tns="drm:MultiDrmCommon/v1/schemas" targetNamespace="drm:MultiDrmCommon/v1/schemas" elementFormDefault="qualified" attributeFormDefault="unqualified">
	<xsd:simpleType name="UUIDType">
		<xsd:restriction base="xsd:token">
			<xsd:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="DrmContentType">
		<xsd:annotation>
			<xsd:documentation>Holds the identification, the definition of a content and the associated behavior of the key and signalization management</xsd:documentation>
		</xsd:annotation>
		<xsd:all>
			<xsd:element name="drmContentId" type="tns:DrmContentIdType">
				<xsd:annotation>
					<xsd:documentation>The content id as provided by the CMS to the encoder</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="profile" type="tns:SecurityProfileType">
				<xsd:annotation>
					<xsd:documentation>Defines how the key and the signalization are managed</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="DistributionModeType">
		<xsd:restriction base="xsd:token">
			<xsd:enumeration value="VOD"/>
			<xsd:enumeration value="LIVE"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="ContentKeyType">
		<xsd:all>
			<xsd:element name="keyId" type="tns:UUIDType">
				<xsd:annotation>
					<xsd:documentation>The identifier assigned to a given content key. Provided as a UUID</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="key" type="xsd:base64Binary" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The content key, depending on the use case it may or not be provided here</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:complexType name="DrmType">
		<xsd:sequence>
			<xsd:element name="drmSystemId" type="tns:UUIDType">
				<xsd:annotation>
					<xsd:documentation>The DRM system id, defined as a UUID</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="drmName" type="xsd:string" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>The DRM name, optional</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DrmListType">
		<xsd:sequence>
			<xsd:element name="drm" type="tns:DrmInfoType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>The list of addressed DRM</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:sequence>
	</xsd:complexType>
	<xsd:complexType name="DrmSignalizationType">
		<xsd:choice>
			<xsd:element name="dash" type="tns:DashSignalizationType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>DASH signalization as a MPD content protection XML section and/or a PSSH header per DRM</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="hls" type="tns:HlsSignalizationType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>HLS signalization as a URI  string per DRM</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="piff" type="tns:PiffSignalizationType" maxOccurs="unbounded">
				<xsd:annotation>
					<xsd:documentation>PIFF signalization as a PSSH XML header per DRM</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:choice>
	</xsd:complexType>
	<xsd:complexType name="SecurityProfileType">
		<xsd:all>
			<xsd:element name="distributionMode" type="tns:DistributionModeType">
				<xsd:annotation>
					<xsd:documentation>This mode allows to know how to manage the keys: VOD means only one key for the content, LIVE means key rotation</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="streamingMode" type="tns:StreamingModeType" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Provides the way the content is streamed, helps build the signalization. If omitted no signalization will be provided in return</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="emi" type="xsd:unsignedShort" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Encryption method indicator (Nagra Spec)</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
			<xsd:element name="cryptoPeriod" type="xsd:unsignedInt" minOccurs="0">
				<xsd:annotation>
					<xsd:documentation>Optional crypto period in seconds. Not applicable if Sunrise key change mode is set</xsd:documentation>
				</xsd:annotation>
			</xsd:element>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="StreamingModeType">
		<xsd:restriction base="xsd:token">
			<xsd:enumeration value="DASH"/>
			<xsd:enumeration value="HLS"/>
			<xsd:enumeration value="PIFF"/>
			<xsd:enumeration value="CUSTOM"/>
		</xsd:restriction>
	</xsd:simpleType>
	<xsd:complexType name="DashSignalizationType">
		<xsd:complexContent>
			<xsd:extension base="tns:GenericSignalizationType"/>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="DrmInfoType">
		<xsd:complexContent>
			<xsd:extension base="tns:DrmType">
				<xsd:sequence>
					<xsd:element name="drmMetadata" type="xsd:base64Binary" minOccurs="0">
						<xsd:annotation>
							<xsd:documentation>DRM specific metadata</xsd:documentation>
						</xsd:annotation>
					</xsd:element>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="PiffSignalizationType">
		<xsd:complexContent>
			<xsd:extension base="tns:GenericSignalizationType"/>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="HlsSignalizationType">
		<xsd:complexContent>
			<xsd:extension base="tns:DrmType">
				<xsd:sequence>
					<xsd:element name="keyUri" type="xsd:anyURI"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="GenericSignalizationType">
		<xsd:complexContent>
			<xsd:extension base="tns:DrmType">
				<xsd:sequence>
					<xsd:element name="manifestHeader" type="xsd:string" minOccurs="0"/>
					<xsd:element name="psshBox" type="tns:PsshBoxType" minOccurs="0"/>
				</xsd:sequence>
			</xsd:extension>
		</xsd:complexContent>
	</xsd:complexType>
	<xsd:complexType name="PsshBoxType">
		<xsd:all>
			<xsd:element name="data" type="xsd:base64Binary"/>
		</xsd:all>
	</xsd:complexType>
	<xsd:simpleType name="DrmContentIdType">
		<xsd:restriction base="xsd:string"/>
	</xsd:simpleType>
</xsd:schema>
