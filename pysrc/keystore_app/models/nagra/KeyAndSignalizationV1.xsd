<?xml version="1.0" encoding="UTF-8"?>
<!-- edited with XMLSpy v2008 (http://www.altova.com) by <PERSON><PERSON><PERSON><PERSON> (Nagravision SA) -->
<xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:mdc_v1="drm:MultiDrmCommon/v1/schemas" xmlns:kas_v1="drm:KeyAndSignalization/v1/schemas" targetNamespace="drm:KeyAndSignalization/v1/schemas" elementFormDefault="qualified" attributeFormDefault="unqualified">
    <xsd:import namespace="drm:MultiDrmCommon/v1/schemas" schemaLocation="MultiDrmCommonV1.xsd"/>
    <xsd:simpleType name="KeyAndSignalizationReturnCodeType">
        <xsd:restriction base="xsd:token">
            <xsd:enumeration value="OK"/>
            <xsd:enumeration value="UNDEFINED_DRM_SYSTEM_ID"/>
            <xsd:enumeration value="UNDEFINED_STREAMING_MODE"/>
            <xsd:enumeration value="UNDEFINED_DISTRIBUTION_MODE"/>
            <xsd:enumeration value="INTERNAL_ERROR"/>
            <xsd:enumeration value="UNAVAILABLE_SERVICE"/>
            <xsd:enumeration value="UNDEFINED_ENCRYPTION_METHOD"/>
            <xsd:enumeration value="INVALID_METADATA"/>
            <xsd:enumeration value="INCONSISTENT_CONTENT"/>
            <xsd:enumeration value="MISSING_KEY_ID"/>
            <xsd:enumeration value="MISSING_CONTENT_KEY"/>
            <xsd:enumeration value="MISSING_STREAMING_MODE"/>
            <xsd:enumeration value="MISSING_ENCRYPTION_METHOD"/>
            <xsd:enumeration value="INVALID_KEY_LENGTH"/>
            <xsd:enumeration value="ALREADY_EXISTING_KEY_ID"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:complexType name="GetKeyAndSignalizationResponseType">
        <xsd:all>
            <xsd:element name="status" type="kas_v1:KeyAndSignalizationReturnCodeType"/>
            <xsd:element name="errorMessage" type="xsd:string" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Optional error message in case the request went wrong.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contentKey" type="mdc_v1:ContentKeyType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>The content key returned to the encoder, the key element is mandatory if generated by KSS. May be omitted if key(s) provided by the encoders, and omitted if an error occured.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="drmSignalization" type="mdc_v1:DrmSignalizationType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>The signalization information according to the provided streaming mode and a DRM system</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:all>
    </xsd:complexType>
    <xsd:complexType name="ScheduledKeyType">
        <xsd:all>
            <xsd:element name="time" type="xsd:unsignedLong">
                <xsd:annotation>
                    <xsd:documentation>Time at which the key shall be valid. For a file based scrambler this will be the offset in seconds from the start of the file. For a live channel scrambler this will be the UTC POSIX time</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="contentKey" type="mdc_v1:ContentKeyType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>The content key provided by the encoder</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:all>
    </xsd:complexType>
    <xsd:complexType name="GetKeyAndSignalizationRequestType">
        <xsd:all>
            <xsd:element name="drmContent" type="mdc_v1:DrmContentType">
                <xsd:annotation>
                    <xsd:documentation>The content for which a key and signalization are required</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="drmList" type="mdc_v1:DrmListType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Holds the list of DRM for which the content is shared. If missing, the configured DRM list is used instead </xsd:documentation>
                </xsd:annotation>
            </xsd:element>
            <xsd:element name="scheduledKey" type="kas_v1:ScheduledKeyType" minOccurs="0">
                <xsd:annotation>
                    <xsd:documentation>Holds the key provided by the encoder. If missing, the key is generated and managed by the system.</xsd:documentation>
                </xsd:annotation>
            </xsd:element>
        </xsd:all>
    </xsd:complexType>
    <xsd:complexType name="HeartbeatRequestType">
        <xsd:all>
            <xsd:element name="version" type="xsd:string" minOccurs="0"/>
        </xsd:all>
    </xsd:complexType>
    <xsd:complexType name="HeartbeatResponseType">
        <xsd:all>
            <xsd:element name="returnCode" type="kas_v1:HeartbeatReturnCodeType"/>
            <xsd:element name="errorMessage" type="xsd:string" minOccurs="0"/>
            <xsd:element name="status" type="kas_v1:SystemStatusType"/>
        </xsd:all>
    </xsd:complexType>
    <xsd:simpleType name="HeartbeatReturnCodeType">
        <xsd:restriction base="xsd:token">
            <xsd:enumeration value="OK"/>
            <xsd:enumeration value="UNSUPPORTED_VERSION"/>
            <xsd:enumeration value="INTERNAL_ERROR"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:simpleType name="SystemStatusType">
        <xsd:restriction base="xsd:token">
            <xsd:enumeration value="ACTIVE"/>
            <xsd:enumeration value="STANDBY"/>
        </xsd:restriction>
    </xsd:simpleType>
    <xsd:element name="GetKeyAndSignalizationRequest" type="kas_v1:GetKeyAndSignalizationRequestType"/>
    <xsd:element name="GetKeyAndSignalizationResponse" type="kas_v1:GetKeyAndSignalizationResponseType"/>
    <xsd:element name="HeartbeatRequest" type="kas_v1:HeartbeatRequestType"/>
    <xsd:element name="HeartbeatResponse" type="kas_v1:HeartbeatResponseType"/>
</xsd:schema>
