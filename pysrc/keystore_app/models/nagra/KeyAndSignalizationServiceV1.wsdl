<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wsdl:definitions
  name="IKeyAndSignalizationV1"
  targetNamespace="drm:KeyAndSignalization/v1/service"
  xmlns:tns="drm:KeyAndSignalization/v1/service"
  xmlns:defs="drm:KeyAndSignalization/v1/definitions"
  xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
  xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/">

  <wsdl:import namespace="drm:KeyAndSignalization/v1/definitions" location="KeyAndSignalizationV1.wsdl" />

  <wsdl:binding name="KeyAndSignalizationV1SoapBinding" type="defs:KeyAndSignalizationPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    
    <wsdl:operation name="getKeyAndSignalization">
      <soap:operation soapAction="cks-ws-keyAndSignalization"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    
    <wsdl:operation name="heartbeat">
      <soap:operation soapAction="cks-ws-keyAndSignalization"/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
   
  </wsdl:binding>
  
  <wsdl:service name="KeyAndSignalizationV1Service"> 
    <wsdl:port name="KeyAndSignalizationV1SoapPort" binding="tns:KeyAndSignalizationV1SoapBinding">
      <soap:address location="cks-ws-keyAndSignalization" />
     </wsdl:port> 
  </wsdl:service>
  
</wsdl:definitions>
