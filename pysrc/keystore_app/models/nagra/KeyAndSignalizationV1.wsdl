<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<wsdl:definitions 
    name="KeyAndSignalizationV1" 
    targetNamespace="drm:KeyAndSignalization/v1/definitions" 
    xmlns:tns="drm:KeyAndSignalization/v1/definitions" 
    xmlns:kas_v1="drm:KeyAndSignalization/v1/schemas" 
    xmlns:mdc_v1="drm:MultiDrmCommon/v1/schemas" 
    xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" 
    xmlns:xsd="http://www.w3.org/2001/XMLSchema">

    <wsdl:types>
        <xsd:schema targetNamespace="drm:KeyAndSignalization/v1/definitions">
            <xsd:import namespace="drm:MultiDrmCommon/v1/schemas" schemaLocation="MultiDrmCommonV1.xsd"/>
            <xsd:import namespace="drm:KeyAndSignalization/v1/schemas" schemaLocation="KeyAndSignalizationV1.xsd"/>
        </xsd:schema>
    </wsdl:types>

    <wsdl:message name="GetKeyAndSignalizationRequest">
        <wsdl:part name="body" element="kas_v1:GetKeyAndSignalizationRequest"/>
    </wsdl:message>

    <wsdl:message name="GetKeyAndSignalizationResponse">
        <wsdl:part name="body" element="kas_v1:GetKeyAndSignalizationResponse"/>
    </wsdl:message>

    <wsdl:message name="HeartbeatRequest">
        <wsdl:part name="body" element="kas_v1:HeartbeatRequest" />
    </wsdl:message>

    <wsdl:message name="HeartbeatResponse">
        <wsdl:part name="body" element="kas_v1:HeartbeatResponse" />
    </wsdl:message>

    <wsdl:portType name="KeyAndSignalizationPortType">
        <wsdl:operation name="getKeyAndSignalization">
            <wsdl:input message="tns:GetKeyAndSignalizationRequest" name="KeyAndSignalizationInput"/>
            <wsdl:output message="tns:GetKeyAndSignalizationResponse"/>
        </wsdl:operation>
        <wsdl:operation name="heartbeat">
            <wsdl:input message="tns:HeartbeatRequest" name="heartbeatInput"/>
            <wsdl:output message="tns:HeartbeatResponse"/>
        </wsdl:operation>
    </wsdl:portType>

</wsdl:definitions>
