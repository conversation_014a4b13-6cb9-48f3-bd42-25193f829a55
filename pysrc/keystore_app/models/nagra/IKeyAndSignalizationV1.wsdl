<wsdl:definitions name='IKeyAndSignalizationV1' targetNamespace='drm:KeyAndSignalization/v1/service' xmlns:defs='drm:KeyAndSignalization/v1/definitions' xmlns:soap='http://schemas.xmlsoap.org/wsdl/soap/' xmlns:tns='drm:KeyAndSignalization/v1/service' xmlns:wsdl='http://schemas.xmlsoap.org/wsdl/'>
 <wsdl:import location='http://vm-nagrasrv01.localdomain:8280/cks-ws-keyAndSignalization/key?wsdl&amp;resource=KeyAndSignalizationV1.wsdl' namespace='drm:KeyAndSignalization/v1/definitions'></wsdl:import>
 <wsdl:binding name='KeyAndSignalizationV1SoapBinding' type='defs:KeyAndSignalizationPortType'>
  <soap:binding style='document' transport='http://schemas.xmlsoap.org/soap/http'/>
  <wsdl:operation name='getKeyAndSignalization'>
   <soap:operation soapAction='cks-ws-keyAndSignalization'/>
   <wsdl:input>
    <soap:body use='literal'/>
   </wsdl:input>
   <wsdl:output>
    <soap:body use='literal'/>
   </wsdl:output>
  </wsdl:operation>
  <wsdl:operation name='heartbeat'>
   <soap:operation soapAction='cks-ws-keyAndSignalization'/>
   <wsdl:input>
    <soap:body use='literal'/>
   </wsdl:input>
   <wsdl:output>
    <soap:body use='literal'/>
   </wsdl:output>
  </wsdl:operation>
 </wsdl:binding>
 <wsdl:service name='KeyAndSignalizationV1Service'>
  <wsdl:port binding='tns:KeyAndSignalizationV1SoapBinding' name='KeyAndSignalizationV1SoapPort'>
   <soap:address location='http://vm-nagrasrv01.localdomain:8280/cks-ws-keyAndSignalization/key'/>
  </wsdl:port>
 </wsdl:service>
</wsdl:definitions>