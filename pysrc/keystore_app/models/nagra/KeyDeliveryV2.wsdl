<definitions name='KeyDeliveryV2ImplService' targetNamespace='http://www.nagra.com/KeyDelivery-v2' xmlns='http://schemas.xmlsoap.org/wsdl/' xmlns:ns1='http://www.nagra.com/KeyDelivery-v2.0' xmlns:soap='http://schemas.xmlsoap.org/wsdl/soap/' xmlns:tns='http://www.nagra.com/KeyDelivery-v2' xmlns:xsd='http://www.w3.org/2001/XMLSchema'>
 <types>
  <xs:schema targetNamespace='http://www.nagra.com/KeyDelivery-v2' version='1.0' xmlns:ns1='http://www.nagra.com/KeyDelivery-v2.0' xmlns:xs='http://www.w3.org/2001/XMLSchema'>
   <xs:import namespace='http://www.nagra.com/KeyDelivery-v2.0'/>
   <xs:element name='getKeyRequest' nillable='true' type='ns1:getKeyRequest'/>
   <xs:element name='getKeyResponse' nillable='true' type='ns1:getKeyResponse'/>
   <xs:element name='importKeyRequest' nillable='true' type='ns1:importKeyRequest'/>
   <xs:element name='importKeyResponse' nillable='true' type='ns1:importKeyResponse'/>
  </xs:schema>
  <xs:schema targetNamespace='http://www.nagra.com/KeyDelivery-v2.0' version='1.0' xmlns:tns='http://www.nagra.com/KeyDelivery-v2.0' xmlns:xs='http://www.w3.org/2001/XMLSchema'>
   <xs:complexType name='getKeyRequest'>
    <xs:sequence>
     <xs:element name='distributionMode' type='xs:string'/>
     <xs:element name='contentId' type='xs:string'/>
     <xs:element minOccurs='0' name='encryptionMethodIndicator' type='xs:unsignedShort'/>
     <xs:element minOccurs='0' name='cryptoTime' type='xs:unsignedInt'/>
    </xs:sequence>
   </xs:complexType>
   <xs:complexType name='getKeyResponse'>
    <xs:sequence>
     <xs:element name='returnCode' type='tns:getKeyResponseReturnCode'/>
     <xs:element minOccurs='0' name='key' type='xs:hexBinary'/>
     <xs:element minOccurs='0' name='keyURI' type='xs:string'/>
     <xs:element minOccurs='0' name='errorMessage' type='xs:string'/>
    </xs:sequence>
   </xs:complexType>
   <xs:complexType name='importKeyRequest'>
    <xs:sequence>
     <xs:element name='distributionMode' type='xs:string'/>
     <xs:element minOccurs='0' name='contentId' type='xs:unsignedInt'/>
     <xs:element name='key' type='xs:hexBinary'/>
     <xs:element minOccurs='0' name='encryptionMethodIndicator' type='xs:unsignedShort'/>
     <xs:element minOccurs='0' name='cryptoTime' type='xs:unsignedInt'/>
    </xs:sequence>
   </xs:complexType>
   <xs:complexType name='importKeyResponse'>
    <xs:sequence>
     <xs:element name='returnCode' type='tns:importKeyResponseReturnCode'/>
     <xs:element minOccurs='0' name='keyURI' type='xs:string'/>
     <xs:element minOccurs='0' name='errorMessage' type='xs:string'/>
    </xs:sequence>
   </xs:complexType>
   <xs:simpleType name='getKeyResponseReturnCode'>
    <xs:restriction base='xs:string'>
     <xs:enumeration value='KEY_READY'/>
     <xs:enumeration value='INVALID_CONTENT_ID'/>
     <xs:enumeration value='BAD_REQUEST_FORMAT'/>
     <xs:enumeration value='EMI_NOT_SUPPORTED'/>
     <xs:enumeration value='EMI_MISMATCH'/>
     <xs:enumeration value='INTERNAL_SERVER_ERROR'/>
    </xs:restriction>
   </xs:simpleType>
   <xs:simpleType name='importKeyResponseReturnCode'>
    <xs:restriction base='xs:string'>
     <xs:enumeration value='CONTENT_KEY_INSERTED'/>
     <xs:enumeration value='CONTENT_KEY_ALREADY_EXISTS'/>
     <xs:enumeration value='CONTENT_KEY_AND_EMI_MISMATCH'/>
     <xs:enumeration value='BAD_REQUEST_FORMAT'/>
     <xs:enumeration value='EMI_NOT_SUPPORTED'/>
     <xs:enumeration value='INTERNAL_SERVER_ERROR'/>
    </xs:restriction>
   </xs:simpleType>
  </xs:schema>
 </types>
 <message name='KeyDeliveryV2_getKeyResponse'>
  <part element='tns:getKeyResponse' name='parameters'></part>
 </message>
 <message name='KeyDeliveryV2_getKey'>
  <part element='tns:getKeyRequest' name='parameters'></part>
 </message>
 <message name='KeyDeliveryV2_importKeyResponse'>
  <part element='tns:importKeyResponse' name='parameters'></part>
 </message>
 <message name='KeyDeliveryV2_importKey'>
  <part element='tns:importKeyRequest' name='parameters'></part>
 </message>
 <portType name='KeyDeliveryV2'>
  <operation name='getKey' parameterOrder='parameters'>
   <input message='tns:KeyDeliveryV2_getKey'></input>
   <output message='tns:KeyDeliveryV2_getKeyResponse'></output>
  </operation>
  <operation name='importKey' parameterOrder='parameters'>
   <input message='tns:KeyDeliveryV2_importKey'></input>
   <output message='tns:KeyDeliveryV2_importKeyResponse'></output>
  </operation>
 </portType>
 <binding name='KeyDeliveryV2Binding' type='tns:KeyDeliveryV2'>
  <soap:binding style='document' transport='http://schemas.xmlsoap.org/soap/http'/>
  <operation name='getKey'>
   <soap:operation soapAction='cks-nagra-ws/key'/>
   <input>
    <soap:body use='literal'/>
   </input>
   <output>
    <soap:body use='literal'/>
   </output>
  </operation>
  <operation name='importKey'>
   <soap:operation soapAction='cks-nagra-ws/import'/>
   <input>
    <soap:body use='literal'/>
   </input>
   <output>
    <soap:body use='literal'/>
   </output>
  </operation>
 </binding>
 <service name='KeyDeliveryV2ImplService'>
  <port binding='tns:KeyDeliveryV2Binding' name='tns:KeyDeliveryV2SOAP'>
   <soap:address location='http://vm-nagrasrv01.localdomain:8280/cks-ws-nagra/key'/>
  </port>
 </service>
</definitions>