# System
import requests, urllib.parse

# keystore_app package
from ... import settings
from ..providers import ProviderException, Provider, pre_delete_handler

__all__ = ["FairPlayProvider", "FairPlayException"]


class FairPlayException(ProviderException):
    """ Empty class for FairPlay specific exceptions. """

class FairPlayProvider(Provider):
    """
    FairPlayProvider provides a mechanism for interacting with Apple's FairPlay DRM protocol.
    """
    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = "keystore_app"
        verbose_name = "FairPlay provider"
    _key_uri = None

    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        Not really needed, since we don't push data to fairplay, but it keeps provider processing
        consistent regardless of type.
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        self.set_key_uri(key.guid)
        key.providers_meta.append(self.to_dict())

    @staticmethod
    def check_connection(use_timeout=settings.fairplay_timeout, url=settings.fairplay_url):
        """
        Method used to check the network connection to the FairPlay server.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.  It will use the settings default value if none is supplied.
        :return True if it work, otherwise it will throw a FairPlayException
        """
        try:
            parts = urllib.parse.urlparse(url)
            url = "http://" + parts.netloc  # just rip out the hostname (and port if given)
            r = requests.get(url, timeout=use_timeout)
            r.raise_for_status()
            return True
        except Exception as ex:
            raise FairPlayException(str(ex))

    def to_dict(self):
        """
        Method used to convert this provider to a dictionary.  All the uri changes deal with taking the fairplay
        uri from the config, changing the scheme to http, and tacking 'certificate' on the end.
        """
        return {
            "name": self.name,
            "type": self.get_type(),
            "key_uri": self._key_uri,
            "key_format": "com.apple.streamingkeydelivery",  # for the KEYFORMAT value in EXT-X-KEY tag
            "key_format_version": 1,
            #"cert_uri": cert_uri
            #"scheme_id_uri": "urn:uuid:29701FE4-3CC7-4A34-8C5B-AE90C7439A47",
        }

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom, or the value in the config file.
        :return The url for contacting this provider.
        """
        return self.url or settings.fairplay_url

    def set_key_uri(self, key_id):
        """
        Method used to generate a key_uri for a given key.
        :param key_id - he ID of the key to generate the key_uri for.
        :return The key_uri for the key passed in.
        """
        base_url = self.get_url()
        if base_url.endswith('/'):
            base_url = base_url[:-1]
        self._key_uri = "%s/%s" % (base_url, key_id.hex)
        return self._key_uri

    def get_type(self):
        """ Method used to get the type of this providers as a string. """
        return "fairplay"


# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=FairPlayProvider)
