# Django packages
from django.db import models

# System
import uuid


class Stream(models.Model):
    """
    This class stores general information about a stream.  This shouldn't be confused
    with information about a particular segment of the stream.
    """
    id = models.UUIDField('ID', primary_key=True, editable=False)
    channel = models.CharField(max_length=50, blank=True)
    content_id = models.CharField(max_length=50, blank=True)
    encryption_block_size = models.PositiveIntegerField()
    create_time = models.DateTimeField(null=True, blank=True, editable=False)
    segment_duration = models.PositiveIntegerField(null=True)

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'

    @staticmethod
    def valid_id(value):
        """
        Method used to test if a value is a valid stream ID.
        :param value - The value to test if its a valid stream ID.
        :return True/False if the parameter is a valid stream ID.
        """
        try:
            uuid.UUID(value)
            return True
        except ValueError:
            return False

    @property
    def num_used_keys(self):
        return self.keyusage_set.count()

    def __str__(self):
        """ Used to display the id of the stream on the admin page. """
        return str(self.id)
