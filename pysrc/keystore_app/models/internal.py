# System
import socket

# package imports
from .providers import Provider


class InternalProvider(Provider):
    """
    Internal provider for accessing keys directly from this keysyore.
    """
    keystore_name = socket.getfqdn()

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = "keystore_app"

    @staticmethod
    def check_connection(use_timeout=None, url=None):
        """
        Method used to check the network connection to the provider, but since this class is
        for the keystore itself, it always just returns true.
        :return True
        """
        return True


    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        self.set_key_uri(key.guid)
        key.providers_meta.append(self.to_dict())


    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        key_uri = self.key_uri if hasattr(self, "key_uri") else None
        return {
            "type": self.get_type(),
            "name": self.name,
            "key_uri": key_uri,
            "pssh": "",  # actually want an empty string
            "scheme_id_uri": "urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b"  # value for Clear Key
        }

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom, or the address of
        the running keystore.
        :return The url for contacting this provider.
        """
        if self.url is None:
            return "http://%s/key" % InternalProvider.keystore_name
        return self.url

    def get_type(self):
        """
        Method used to get the type of this provider as a string.
        :return The string "internal"
        """
        return "internal"

    def set_key_uri(self, key_id):
        """
        Method used to generate a key_uri for a given key.
        :param key_id The ID of the key to generate the key_uri for.
        :return The key_uri for the key passed in.
        """
        self.key_uri = "%s/raw/%s" % (self.get_url(), key_id.hex)  # pylint: disable=W0201
        return self.key_uri
