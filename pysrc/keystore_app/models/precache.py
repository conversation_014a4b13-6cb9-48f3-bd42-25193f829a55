# Django packages
from django.db import models
from django.utils import timezone


class PreCachedKeys(models.Model):
    key_config = models.ForeignKey("KeyConfig", on_delete=models.CASCADE)
    content_id = models.CharField(max_length=50)

    class Meta(object):
        unique_together = ("key_config", "content_id")
        verbose_name = "PreCached Keys"
        verbose_name_plural = "PreCached Keys"


    def get_unused_key(self):
        """
        Method used to get a Precached key, using the settings from this object.
        @return A new key that is no longer associated with this PreCachedKey object.
        """
        from . import Key
        all_unused = Key.objects.filter(cached_by=self).order_by("created")
        result = all_unused.first()
        if result is None:
            result = self._generate_key(False)
        else:
            result.created = timezone.now()  # reset created time to be now, as if just created
            result.accessed = result.created
        result.cached_by = None
        result.save()
        return result

    def _generate_key(self, cache_key):
        """
        Helper method to create a new key that has the same settings as this object.
        @return A newly created key that has already been saved to the database.
        """
        from . import Key
        new_key = Key.create(self.key_config, self.content_id)
        if cache_key:
            new_key.cached_by = self
        new_key.save()  # notice there is no KeyUsage being created for this key
        return new_key

    @property
    def num_unused(self):
        from . import Key
        return Key.objects.filter(cached_by=self).count()

    def load_cache(self):
        """
        Helper function to make sure the proper number of keys are already created.  If we have enough (or
        more than required), this method will have no effect.
        """
        num_existing = self.num_unused
        while num_existing < self.key_config.pre_create_keys:  # pylint: disable=no-member
            self._generate_key(True)
            num_existing += 1
