# pylint: disable=W0201
# django
from django.db import models

# System
import requests, datetime, urllib.parse, pickle, fcntl, os, sys
import xml.etree.ElementTree as ET
from contextlib import contextmanager

# package imports
from ... import settings, logger
from ..providers import ProviderException, Provider, pre_delete_handler


class KVHLSException(ProviderException):
    """ Empty class for SecureMedia specific exceptions. """


class SecureMediaProvider(Provider):
    """
    SecureMediaProvider is a client to access the 3rd party integration for KVHLS.  It retrieves a "chunk key" for
    either VOD or DTV/live.
    """
    bands_url = models.URLField("URL (bdremote)", blank=True, null=True)

    # here to ensure that if parent version ever changes, we still only have 1 allowed secure media provider
    ALLOWED_INSTANCES = 1

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'


    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        Not really needed, since we don't usually call this way, but it keeps provider processing
        consistent regardless of type
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        self.set_key_uri(key.guid.hex)
        key.providers_meta.append(self.to_dict())


    def set_key_uri(self, uri):
        self.key_uri = uri  # TODO: is this value ever used?


    @staticmethod
    def get_all_bands(use_timeout=settings.kvhls_timeout):
        """
        Used to get a list of all the band names from all the various SecureMedia providers in the system.
        :param use_time - The timeout value to use when contacting all of the server.
        :return A dictionary containing the band IDs, each with a list of providers names that have that band ID.
        """
        result = {}
        for sm in SecureMediaProvider.objects.all():
            bands = sm.update_band_names(use_timeout)
            for b in bands:
                if b not in result:
                    result[b] = [sm.name]
                else:
                    result[b].append(sm.name)
        return result

    def update_band_names(self, use_timeout=settings.kvhls_timeout):
        """
        This method first gets a lock on a file to prevent multiple processes from executing this code at
        the same time.  It then checks for another file on disk containing cached info from the SM server.
        If  the cached file is more than 1 minute old, it hits the server to get new data, which is dumped
        to the file.  The value coming back is XML, so we use ElementTree to parse it and look for all
        the band names.
        :param use_timeout - the timeout value to use, in seconds.
        :return A list of band names found.
        """
        result = []
        rsp = None
        with filelock("band_names_%s" % self.name) as _lock_file:
            now = datetime.datetime.now()
            pickle_path = os.path.join(sys.prefix, "var", "run", "band_names_%s.pickle" % self.name)
            if os.path.exists(pickle_path):
                stats = os.stat(pickle_path)
                if stats.st_size > 0 and stats.st_mtime >= int(now.strftime("%s")) - 60:  # less than 60sec old
                    with open(pickle_path, 'rb') as pick:
                        result = pickle.load(pick)
                else:
                    os.remove(pickle_path)
            if not result:
                try:
                    rsp = requests.get(self.get_bands_url(), timeout=use_timeout)
                    rsp.raise_for_status()
                    root = ET.fromstring(rsp.content)
                    # assumes there is no namespace in the xml
                    for node in root.findall("band"):
                        name = node.get("name")
                        if name:
                            result.append(name)
                    result.sort()
                except Exception:  # pylint: disable=W0703
                    if rsp is None:
                        logger.error("Problem getting the list of band names from Secure Media, failed to connect")
                    else:
                        logger.error("Problem getting the list of band names from Secure Media, status code: %i",
                                rsp.status_code)
                with open(pickle_path, 'wb') as pick:
                    pickle.dump(result, pick)
        return result

    @staticmethod
    def check_connection(use_timeout=settings.kvhls_timeout, url=settings.kvhls_url):
        """
        Method used to check the network connection to the provider.  Just looks for a valid
        root webpage and makes sure the string 'KVHLS' appears somewhere on it.  This is done
        to prevent valid non-secure media servers from passing the connection test.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.  It will use the settings
            default value if none is supplied.
        :return True if it work, otherwise it will throw a KVHLSException
        """
        try:
            r = requests.get(url[:url.rfind('/')], timeout=use_timeout)
            r.raise_for_status()
            if 'KVHLS' in r.text:
                return True
        except Exception as ex:
            raise KVHLSException(str(ex))
        raise KVHLSException('Supplied url is not a Secure Media server')

    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        return {
            "type": self.get_type(),
            "name": self.name,
            "pcw": self.pcw,
            "mkid": self.sm_kid,  # secure media key ID
            "key_uri": self.key_uri
        }

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom,
        or the value in the config file.
        :return The url for contacting this provider.
        """
        return self.url or settings.kvhls_url

    def get_bands_url(self):
        """
        Method used to get the url for listing bands.  It can either be custom,
        or the value in the config file.
        :return The url for obtaining bands from this provider.
        """
        return self.bands_url or settings.bands_url

    def get_type(self):
        """ Method used to get the type of this providers as a string. """
        return 'securemedia'

    def get_chunk_key(self, key_config, content_id):
        """
        Used to obtain a new key from the secure media provider.
        :param key_config - The KeyConfig using this provider.
        :param content_id - The content ID to pass to the Secure Media server.  In the event the KeyConfig
            is live, it will pull the band_id out of the keyconfig
        :return The 'secret' value for the key, or 'control word' in Secure Media terminology.
        """
        try:
            if key_config.is_live():
                content_type = "DTV"
                content_id = key_config.band_id
            else:
                content_type = "VOD"

            r = requests.get(self.get_url(), params={'r': content_id, 't': content_type},
                             timeout=settings.kvhls_timeout)
            r.raise_for_status()  # raises exception on non-200 responses
            lh = r.headers.get('Location')

            # +4 to remove 'pcw=' from output : only want to return base-64 encoded protected control word (pcw)
            self.pcw = lh[lh.rindex('pcw=') + 4:]
            self.sm_kid = r.headers.get('x-sm-kid')

            # create the key_uri, replacing the hostname/port with localhost
            url_parts = urllib.parse.urlparse(lh)
            self.key_uri = lh.replace(url_parts.netloc, "localhost")
            #self.key_uri = lh
            return r.content  # control word in the clear
        except Exception as ex:
            logger.exception("get_chunk_key FAILED: %s", ex)
            raise KVHLSException(str(ex))

    def save(self, *args, **kwargs):
        """
        Used to ensure that if someone enters a blank bands_url, we store it as None, as opposed to an empty
        string.
        """
        if self.bands_url == '':
            self.bands_url = None
        super(SecureMediaProvider, self).save(*args, **kwargs)


@contextmanager
def filelock(name, unlock_when_done=True):
    """
    Support method used to obtain an exclusive lock on an arbitrary file on disk.  This is used mainly to
    prevent independent processes from executing the same come concurrently.
    """
    lockpath = os.path.join(sys.prefix, 'var', 'run', name + ".lk")
    lockf    = os.open(lockpath, os.O_CREAT | os.O_WRONLY | os.O_RDONLY, 0o600)
    fcntl.flock(lockf, fcntl.LOCK_EX)

    try:
        yield lockf
    finally:
        if unlock_when_done:
            fcntl.flock(lockf, fcntl.LOCK_UN)
            os.close(lockf)


# change verbose name of inherited field
SecureMediaProvider._meta.get_field("url").verbose_name = "URL (kvhls)"


# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=SecureMediaProvider)
