# pylint: disable=E1101,E1120,E1123,unused-wildcard-import
# Django packages
from django.db import models
from django.utils import timezone

# 3rd party packages
from jsonfield import JSONField

# System
import uuid, os, datetime

# keystore_app modules
from pysrc.keystore_app.models.stream import Stream
from pysrc.keystore_app.models.securemedia.kvhls import KVHLSException
from . import SecureMediaProvider
from .precache import PreCachedKeys


class NoPreviousKeyException(Exception):
    """
    Empty exception for the case when the secure media provider isn't responding,
    and there is no previous key we can use instead.
    """
    def __init__(self):
        super().__init__("The secure media provider isn't responding, and there is no previous key we can use instead.")


class Key(models.Model):
    """
    This class stores all the information about a particular key.  For provider information
    it is stored here as a JSON field, instead of tons of instances of the providers
    in the system.
    """
    AES_128_CBC = 'AES_128_CBC'
    DES3 = 'DES3'
    ALGORITHM_CHOICES = (
        (AES_128_CBC, 'AES-128 CBC'),
        (DES3, 'Triple DES'),
    )
    YEARS_AHEAD = 30
    id = models.UUIDField('ID', primary_key=True, editable=False)
    _secret = models.BinaryField('Encryption Key', db_column='secret')
    created = models.DateTimeField('Created', auto_now_add=True)
    accessed = models.DateTimeField('Last Accessed', auto_now_add=True)
    algorithm = models.CharField(max_length=20, choices=ALGORITHM_CHOICES, default=AES_128_CBC)
    providers_meta = JSONField(default=[])
    config = models.ForeignKey("KeyConfig", on_delete=models.CASCADE)
    content_id = models.CharField(max_length=50, default="")
    has_all_meta = models.BooleanField("Has All Meta", default=False)
    cached_by = models.ForeignKey(PreCachedKeys, null=True, blank=True, on_delete=models.SET_NULL)

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'

    @property
    def secret(self):
        # TODO: we can take out the if statement when we switch the unit test
        # database to Postgres.
        # This if statement fixes an issue encountered when updating to Django
        # 2.2.6 where a binary field in Postgres returned a memoryview but in
        # Sqlite it was a bytes object, making the unit tests pass and the
        # keystore fail in actual usage, or vice versa
        if isinstance(self._secret, memoryview):
            return self._secret.tobytes()
        return self._secret

    @secret.setter
    def secret(self, secret):
        self._secret = secret

    @staticmethod
    def create(key_conf, content_id):  # pylint: disable=R0912
        """
        Method used to create an instance of this class, using the passed in
        KeyConfig to indicate which providers should be associated with this key.
        It contacts the providers an either gets or sets the key with them as
        required.
        :param key_config - a KeyConfig indicating which providers should be associated
            with this key.
        :param content_id - The content ID/description of the stream being worked on.
        :return An instance of this class.
        """
        k = Key(id=uuid.uuid4(), config=key_conf, content_id=content_id)

        provs = key_conf.all_providers()
        if not provs:  # no providers, generate random key ourselves
            k.secret = os.urandom(16)
        else:

            try:
                # only SecureMedia tells us what the key is, in all other cases, we tell them what it is.
                #  so find a SecureMedia key if one exists ...
                prov = next(x for x in provs if isinstance(x, SecureMediaProvider))
                k.secret = prov.get_chunk_key(key_conf, content_id)  # will use band_id if live
                k.providers_meta.append(prov.to_dict())
                provs.remove(prov)  # do not process this one in the loop
            except KVHLSException:  # secure media exception
                # if we can't get a key from secure media, use the most recent key that has is using the same
                # keyconfig, and content_id.  If we find one, we'll just have a new KeyUsage created for it
                latest_key = Key.objects.filter(config=key_conf, content_id=content_id).order_by('-created').first()
                if latest_key is None:
                    raise NoPreviousKeyException
                return latest_key
            except StopIteration:  # no SecureMedia instance found
                k.secret = os.urandom(16)  # create a key number

            for prov in provs:
                prov.push_to_provider(k, content_id)

        if not k.missing_providers():
            k.has_all_meta = True
        k.save()
        return k

    def just_accessed(self):
        """
        Method used to update the accessed time of this key.
        """
        self.accessed = timezone.now()
        self.save()

    def out_of_window(self):
        """
        Method used to determine if a key is out of window or not.  A key is out of window if it was created more than
        'life' hours ago, and hasn't been access in the last 2 weeks.
        """
        life = self.config.lifetime
        if life and life > 0:
            if self.created + datetime.timedelta(hours=life) < timezone.now() and \
                    self.accessed + datetime.timedelta(weeks=2) < timezone.now():
                return True
        return False

    @property
    def has_expired(self):
        """
        Property to determine if this key has expired or not.
        @return A boolean indicating if this key has expired or not.
        """
        now = timezone.now()
        if self.config.lifetime is None or self.config.lifetime <= 0:  # vod
            delta = datetime.timedelta(weeks=52*Key.YEARS_AHEAD)
        else:
            delta = datetime.timedelta(hours=self.config.lifetime)
        return self.created + delta < now

    def expires(self):
        """
        Method used to return a string representation of when this key will expire.
        :return A string with the format 'YYYY-MM-DD hh:mm::ss'
        """
        if self.config.lifetime is None or self.config.lifetime <= 0:  # vod
            delta = datetime.timedelta(weeks=52*Key.YEARS_AHEAD)
        else:
            delta = datetime.timedelta(hours=self.config.lifetime)
        return (self.created + delta).strftime("%Y-%m-%d %H:%M:%S")

    def missing_providers(self):
        """
        Method used to get a list of providers that this key is missing the metadata for.  It gets a list
        of its providers, and check the metadata field trying to match up the "name" field on each entry.
        @return A list of all the providers we're missing metadata for, or an empty list.
        """
        result = []
        for prov in self.config.all_providers():
            match = False
            for meta in self.providers_meta: # pylint: disable=not-an-iterable
                if prov.name == meta.get("name") and prov.get_type() == meta.get("type"):
                    match = True
                    break
            if not match:
                result.append(prov)
        return result

    def generate_missing_meta(self, content_id):
        """
        Used to generate any missing meta-data for providers that weren't responding properly when this
        key was created, or this key is associated with a VOD KeyConfig and it's providers changed.
        :param content_id - The content ID from the web request.
        """
        # intentionally NOT handling SecureMedia here, since we can't push keys TO it
        for prov in self.missing_providers():
            prov.push_to_provider(self, content_id)
        # check and see if they all worked or not
        if not self.missing_providers():
            self.has_all_meta = True
            self.save()

    @property
    def guid(self):
        """
        Used to get the key ID as a UUID object.
        """
        key_guid = self.id if isinstance(self.id, uuid.UUID) else uuid.UUID(self.id)
        return key_guid

    def __str__(self):
        """ Used to display the id of the key on the admin page. """
        return str(self.id)


class KeyUsage(models.Model):
    """
    This class is used to keep track of which key is used to encrypt a specific
    segment of a given stream.  When the user asks for a key for a stream, they
    are really looking for a the matching instance of this class.
    """

    key = models.ForeignKey(Key, on_delete=models.CASCADE)  # auto-creates key_id
    stream = models.ForeignKey(Stream, on_delete=models.CASCADE)  # auto-creates stream_id
    startSeg = models.PositiveIntegerField('Start Segment')
    endSeg = models.PositiveIntegerField('End Segment')

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'
        unique_together = ("stream", "startSeg")

    def to_dict(self):
        """
        Method used to convert this KeyUsage to a dictionary.  This is what gets
        sent back to the user when they do a 'key get' operation.
        """
        result = {
            'algorithm': self.key.algorithm,
            'start_seg': self.startSeg,
            'end_seg': self.endSeg,
            'key': self.key.secret.hex(),
            'providers': self.key.providers_meta,
            'expires': self.key.expires()
        }
        # can't use .hex on the IDs, because since they are foreign keys, not actually UUIDs, so make
        # it a UUID first
        result['key_id'] = self.key_id.hex
        return result

    @property
    def media_id(self):
        return self.key.config.band_id if self.key.config.is_live() else self.stream.content_id

    @property
    def media_type(self):
        return self.key.config.usage
