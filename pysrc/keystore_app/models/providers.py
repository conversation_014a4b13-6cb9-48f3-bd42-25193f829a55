# django
from django.db import models


class ProviderException(Exception):
    """ Empty class to be at the top of a new provider exception hierarchy. """


class Provider(models.Model):
    """
    This class is the top of the provider hierarchy.  Ideally it would be an abstract base
    class, but that doesn't mix well with the django models, so all the abstract methods
    here just assert false if called.
    """
    name = models.CharField('Provider Name', max_length=30, primary_key=True)
    url = models.URLField('Custom URL', blank=True, null=True, unique=True)
    url.default_validators[0].schemes.append("skd") #pylint:disable=no-member

    # the number of instances allowed on any given keyconfig, child classes can override if the want.  A value of 0
    # means there is an unlimited number of instances allowed
    ALLOWED_INSTANCES = 1

    class Meta(object):
        """ Used to tell django which app this model belongs to, and that it's abstract. """
        app_label = 'keystore_app'
        abstract = True

    def __str__(self):
        """ Method for displaying the name of this provider on the admin site. """
        return self.name

    def related_label(self):
        return "" # To keep stupid <PERSON><PERSON><PERSON><PERSON> from showing the provider name twice

    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        assert False, 'method needs to be implemented by child class'

    def push_to_provider(self, key, content_id): # pylint: disable=unused-argument
        """
        method used to send key information to the DRM provider
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        assert False, 'method needs to be implemented by child class'

    @staticmethod
    def check_connection(use_timeout, url):  # pylint: disable=W0613
        """
        Method used to check the network connection to the provider.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.
        """
        assert False, 'method needs to be implemented by child class'

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom,
        or the value in the config file.
        :return The url for contacting this provider.
        """
        assert False, 'method needs to be implemented by child class'

    def get_type(self):
        """
        Method used to get the type of this providers as a string.  It is the cannonical
        name, like 'Nagra', or 'Apple'.
        :return The type for this provider.
        """
        assert False, 'method needs to be implemented by child class'

    def save(self, *args, **kwargs):
        """
        Used to ensure that if someone enters a blank URL, we store it as None, as opposed to an empty
        string.
        """
        if self.url == '':
            self.url = None
        super(Provider, self).save(*args, **kwargs)

    @staticmethod
    def child_classes_query_set():
        """
        Used to get a query set of all the concrete child (and farther down) classes.  It is used mainly in restricting
        the generic relationship in the KeyConfigRelation to only be providers.
        @return A query set containing all the child classes.
        """
        result = None
        children = Provider.__subclasses__()  # pylint: disable=no-member
        while children:
            x = children.pop()
            children.extend(x.__subclasses__())  # add any children next level down
            if x._meta.abstract:
                continue
            if result is None:
                result = models.Q(app_label=x._meta.app_label, model=x._meta.model_name)
            else:
                result = result | models.Q(app_label=x._meta.app_label, model=x._meta.model_name)
        return result


def pre_delete_handler(instance, **kwargs):  # pylint: disable=unused-argument
    """
    When deleting a provider directly, since the KeyConfigRelation uses a generic foreign key
    to instances of providers, they don't cascade delete.  So we have to check all the relations
    and delete them ourselves.
    """
    from pysrc.keystore_app.models.keyconfig import KeyConfigRelation
    for key_rel in KeyConfigRelation.objects.filter(object_id=instance.name):  # partial filter
        if key_rel.content_object == instance:
            key_rel.delete()
