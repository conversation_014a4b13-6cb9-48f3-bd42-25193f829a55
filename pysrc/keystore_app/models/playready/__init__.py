# pylint: disable=W0201
# System
import uuid, base64, requests, urllib.parse, binascii, struct, collections

# 3rd party
from Crypto.Cipher import AES

# keystore_app package
from ... import settings, logger
from ..providers import ProviderException, Provider, pre_delete_handler

__all__ = ["PlayReadyProvider", "PlayReadyException"]


class PlayReadyException(ProviderException):
    """ Empty class for PlayReady specific exceptions. """

class PlayReadyProvider(Provider):
    """
    PlayReadyProvider provides a mechanism for interacting with Microsoft's PlayReady
    DRM protocol.
    """
    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = "keystore_app"
        verbose_name = "PlayReady provider"


    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        try:
            self.set_key(key.guid, key.secret)
            key.providers_meta.insert(0, self.to_dict())  # make sure it's first provider in list
        except PlayReadyException:
            logger.exception("Problem configuring PlayReady meta-data for %s", self.name)

    @staticmethod
    def check_connection(use_timeout=settings.playready_timeout, url=settings.playready_url):
        """
        Method used to check the network connection to the PlayReady license server.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.  It will use the settings default value if none is supplied.
        :return True if it work, otherwise it will throw a PlayReadyException
        """
        try:
            r = requests.get(url, timeout=use_timeout)
            r.raise_for_status()
            return True
        except Exception as ex:
            raise PlayReadyException(str(ex))

    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        url = self.get_url()
        if not url.endswith('/'):
            url += '/'
        pssh = self._create_pssh()

        # need to put in playready header objects (like a TLV) that wraps the pssh
        # the '<' symbol on the struct.pack means pack it as little-endian
        pr_type = binascii.hexlify(struct.pack('<H', 1))  # 2 bytes
        pr_length = binascii.hexlify(struct.pack('<H', len(pssh) // 2))
        pr_header = binascii.hexlify(struct.pack('<I', 4 + 2 + 4 + len(pssh) // 2))
        pr_rec_count = binascii.hexlify(struct.pack('<H', 1))
        pr_rec = pr_type + pr_length + pssh

        return {
            "name": self.name,
            "type": self.get_type(),
            "pssh": (pr_header + pr_rec_count + pr_rec).decode(),
            "la_url": urllib.parse.urljoin(url, "playready/rightsmanager.asmx"),
            "scheme_id_uri": "urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95"
        }

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom, or the value in the config file.
        :return The url for contacting this provider.
        """
        return self.url or settings.playready_url

    def get_type(self):
        """ Method used to get the type of this providers as a string. """
        return "playready"

    def set_key(self, key_id, key):
        """
        Set the key information for this instance.  It calculates the checksum for the key.
        :param key_id - the UUID key_id to be sent to the PlayReady server.
        :param key - the actual key to send to the PlayReady server.
        :raises PlayReadyException - if any of the parameters are invalid.
        """
        # check params
        if not isinstance(key_id, uuid.UUID):
            raise PlayReadyException("invalid key_id")
        if len(key) != 16:
            raise PlayReadyException("invalid key size")

        self.key_id = key_id
        self.checksum = PlayReadyProvider._checksum(key_id, key)
        self.save()

    def _create_pssh(self):
        """
        Used to createa a PlayReady WRMHEADER pssh, using version 4.0.0.0.  It is XML based, so it builds the
        corresponding XML structure with the appropriate tags and attributes.  It then converts it all to a string,
        the encodes the string as utf-16, and finally converts it to a hex form.
        @return The PSSH for playready, in a hexlified form (with the utf-16 bom removed).
        """
        # noinspection PyUnresolvedReferences
        from lxml.etree import Element, SubElement, tostring

        url = self.get_url()
        if not url.endswith('/'):
            url += '/'

        # buid the wrm 4.0.0.0 header, the attributes need to be in a specific order (stupid microsoft), so
        # we need use the OrderedDict
        attributes = collections.OrderedDict([
            ("xmlns", "http://schemas.microsoft.com/DRM/2007/03/PlayReadyHeader"),
            ("version","4.0.0.0")
            ])
        wrm_header = Element("WRMHEADER", attributes)
        data = SubElement(wrm_header, "DATA")
        protect_info = SubElement(data, "PROTECTINFO")
        key_len = SubElement(protect_info, "KEYLEN")
        key_len.text = "16"
        alg_id = SubElement(protect_info, "ALGID")
        alg_id.text = "AESCTR"
        la_url = SubElement(data, "LA_URL")
        la_url.text = urllib.parse.urljoin(url, "playready/rightsmanager.asmx")
        lui_url = SubElement(data, "LUI_URL")
        lui_url.text = urllib.parse.urljoin(url, "login.aspx")
        kid = SubElement(data, "KID")
        kid.text = base64.b64encode(self.key_id.bytes_le).decode()
        checksum = SubElement(data, "CHECKSUM")
        checksum.text = self.checksum

        xml_str = tostring(wrm_header) # can use 'pretty_print=True' for debugging
        return binascii.hexlify(xml_str.decode().encode("utf-16"))[4:]


    @staticmethod
    def _checksum(key_id, content_key):
        """
        Method used to calculate the PlayReady checksum that can go into the PlayReady header
        object.  The client can use this verify the key is correct when it obtains it from the
        license server.  The algorithm used here is defined by Microsoft.
        :param key_id - The guid for the key.
        :param content_key - The actual content key.
        :return The checksum, which is base64 encoded.
        """
        encrypter = AES.new(content_key, AES.MODE_ECB)
        msg = encrypter.encrypt(key_id.bytes_le)
        return base64.b64encode(msg[:8])


# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=PlayReadyProvider)
