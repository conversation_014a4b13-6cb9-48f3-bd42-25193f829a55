# pylint: disable=attribute-defined-outside-init

# django
from django.db import models

# system
import requests, binascii, uuid, urllib.parse

# keystore_app package
from ... import settings, logger
from ..providers import ProviderException, Provider, pre_delete_handler
from .pssh_pb2 import WidevineCencHeader

__all__ = ["WidevineProvider", "WidevineException"]


class WidevineException(ProviderException):
    """ Empty class for Widevine specific exceptions. """


class WidevineProvider(Provider):
    """
    This class  provides a mechanism for sending information about the Widevine proxy to clients.
    The main component is creating the PSSH that dynapack can put into the mpd header.
    """
    provider = models.CharField("Provider Key", max_length=100)

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'
        verbose_name = "Widevine provider"

    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        try:
            self.create_pssh(key.guid, content_id)
            key.providers_meta.append(self.to_dict())
        except WidevineException:
            logger.exception("Problem configuring Widevine meta-data for %s", self.name)

    @staticmethod
    def check_connection(use_timeout=settings.widevine_timeout, url=settings.widevine_url):
        """
        Method used to check the network connection to the Widevine proxy server.
        :param use_timeout - the timeout value to use, in seconds.
        :param url - the url of where the server is.
        :return True if it worked, otherwise it will throw a WidevineException
        """
        try:
            parts = urllib.parse.urlparse(url)
            url = "http://" + parts.netloc  # just rip out the hostname (and port if given)
            r = requests.get(url, timeout=use_timeout)
            r.raise_for_status()
            return True
        except Exception as ex:
            raise WidevineException(str(ex))

    def to_dict(self):
        """ Method used to convert this provider to a dictionary. """
        return {
            "name": self.name,
            "type": self.get_type(),
            "pssh": self.pssh.SerializeToString().hex(),
            "proxy_url": self.get_url(),
            "scheme_id_uri": "urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed",  # widevine constant
        }

    def get_url(self):
        """
        Method used to get the url for this provider.  It can either be custom,
        or the value in the config file.
        :return The url for contacting this provider.
        """
        return self.url or settings.widevine_url

    def get_type(self):
        """
        Method used to get the type of this providers as a string.
        """
        return "widevine"

    def create_pssh(self, key_id, content_id):
        """
        Set the key information for this instance so it can generate the pssh correctly.
        :param key_id - the UUID key_id to be encoded in the PSSH.
        :param content_id - the content ID to be encoded in the PSSH.
        :raises WidevineException - if any of the parameters are invalid.
        """
        # check params
        if not isinstance(key_id, uuid.UUID):
            raise WidevineException("invalid key_id")

        self.pssh = WidevineCencHeader()
        self.pssh.algorithm = 1
        self.pssh.key_id.append(key_id.hex.encode('ASCII'))  # pylint: disable=no-member
        self.pssh.provider = self.provider
        self.pssh.content_id = content_id.encode('ASCII')
        self.pssh.track_type = "SD_HD"
        self.save()

    @staticmethod
    def decode_pssh(pssh):
        decode = binascii.unhexlify(pssh)
        result = WidevineCencHeader()
        result.ParseFromString(decode)
        return result


# change verbose name of inherited field
WidevineProvider._meta.get_field("url").verbose_name = "Proxy URL"

# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=WidevineProvider)
