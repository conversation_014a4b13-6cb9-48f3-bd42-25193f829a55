#pylint: disable=W0201
# System packages
import base64, requests
import socket, time
import json
from urllib.error import URLError
from django.db import models

#keystore_app packages
from ... import settings, logger
from ..providers import ProviderException, Provider, pre_delete_handler

__all__ = ["PrimetimeProvider", "PrimetimeException"]

class PrimetimeException(ProviderException):
    """ Empty class for primetime exceptions """

class PrimetimeProvider(Provider):
    """
    PrimetimeProvider provides a mechanism for interacting with Adobe's primetime
    DRM protocol and systems
    """

    primetime_policy_id = models.Char<PERSON>ield("Policy ID", max_length=100, default="SLING-TVI-LINEAR-QA")

    class Meta(object):
        """ used to tell django which app this mdoel belongs to. """
        app_label = "keystore_app"
        verbose_name = "Primetime provider"

    _key_uri = None

    def push_to_provider(self, key, content_id):
        """
        method used to send key information to the DRM provider
        Method to push to primetime_provider. Currently only adds proper data to internal lists.
        @param key -  the Key object to be pushed
        @param content_id -  The content ID/description of the stream being worked on.
        """
        key_id = str(key.guid)
        secret_key = base64.b64encode(key.secret)
        try:
            self.get_key(key_id=key_id, key=secret_key, content_id=content_id)
            key.providers_meta.append(self.to_dict())
        except PrimetimeException as ex:
            logger.exception("problem talking to primetime: %s", ex)

    @staticmethod
    def check_connection(use_timeout=settings.primetime_timeout, url=settings.primetime_url):
        """
        Method used to check the network connection for the primetime server
        :param use_timeout: the timeout value to use in seconds
        :param url: the url of where the server is. It will use the default value
                    in settings if non is supplied
        :return: True if it worked, otherwise throw a PrimetimeException
        """

        policyId = settings.primetime_policy_id

        data = {
            "keyId": "47c935bf-2604-ace5-4206-1b6bb553c332",
            "key": "a79B+vVtLVg/9wsROJeQtA==",
            "contentId": "1102156515546396143",
            "policyId": policyId,
        }

        headers = {
            'Accept': 'application/vnd.xcal.cs.fms.metadataResponse+json; version=1',
            'Content-Type': 'application/vnd.xcal.cs.fms.metadataRequest+json; version=1'
        }

        try:
            r = requests.post(url, data=json.dumps(data), headers=headers, timeout=use_timeout)
            r.raise_for_status()
            return True
        except Exception as ex:
            raise PrimetimeException(str(ex))

    def to_dict(self):
        """ method used to convert this provider to a dictionary """
        url = self.get_url()
        if not url.endswith("/"):
            url += '/'

        import hashlib  # pylint: disable=import-outside-toplevel
        faxs_cm = self.metadata
        faxs_cm_hash = hashlib.sha1(base64.b64decode(faxs_cm))

        return {
            "name": self.name,
            "type": self.get_type(),
            "key_uri": self.license_server,
            "faxs_cm": faxs_cm,
            "faxs_cm_hash": faxs_cm_hash.hexdigest(),
        }

    def get_url(self):
        """
        method used to get the url for this provider. it can be custom or the default value in the config value
        :return: the url use for contacting this provider
        """
        return self.url or settings.primetime_url

    def get_type(self):
        """
        method sed to get the type of this provider as a string
        :return: a string of the provider. "primetime"
        """
        return "primetime"

    def set_key_uri(self, key_id):
        """
        Method to generate a key_uri for a given key.
        :param key_id: the ID of the key to generate the key_uri for
        :return: the key_uri for th ekey passed in
        """
        base_url = self.get_url()
        if base_url.endswith("/"):
            base_url = base_url[:-1]
        self._key_uri = "%s/%s" % (base_url, key_id.hex)
        return self._key_uri

    def set_license_server(self, license_server):
        """
        Method to set licesnee server returned from the comcast server
        :param license_server: the FQDN of the license server
        :return: nothing
        """
        self.license_server = license_server

    def set_metadata(self, metadata):
        """
        Method to set metadata returned from the comcast server
        :param metadata: the full string of metadata
        :return: nothing
        """
        self.metadata = metadata

    def get_policy_id(self):
        """
        method to get the policy id. defaults in the settings to "SLING-TVI-LINEAR-STAGE".
        Can be set in the configuration under "providers"-"primetime_policy_id"
        :return:  a string of the policy id to use
        """
        if self.primetime_policy_id is None:
            return settings.primetime_policy_id
        return self.primetime_policy_id

    def get_key(self, key_id, key, content_id): # pylint: disable=unused-argument
        '''
        method to actually get a key from the comcast primetime server
        :param key_id: (string) the ID of the key used to encrypt the content
        :param key:  (string) b64 encoded representaiton of the key used to encrypt the content
        :param content_id:  (string) the id of the content
        :return: Nothing
        '''
        payload = {"keyId": key_id,
                   "key": key.decode('latin-1'),
                   "contentId": key_id,
                   "policyId": self.get_policy_id()}
        headers = {
            'Accept': 'application/vnd.xcal.cs.fms.metadataResponse+json; version=1',
            'Content-Type': 'application/vnd.xcal.cs.fms.metadataRequest+json; version=1'
        }
        url = self.get_url()
        retry_count = 0 # this is a just-in-case the server goes down.
        self.set_metadata(None)
        self.set_license_server(None)
        while True:
            if retry_count >= 5:
                raise PrimetimeException("tried primetime 5 times, no response. erroring out")
            try:
                rsp = requests.post(url, data=json.dumps(payload), headers=headers)
            except (socket.timeout, URLError) as ex:
                raise PrimetimeException("Problem contacting Primetime server: {}".format(ex))
            except Exception as ex:
                raise PrimetimeException("General exception trying to connect to server: {}".format(ex))
            if rsp.ok:
                primetime_data = json.loads(rsp.content.decode())
                self.set_metadata(primetime_data["metadata"])
                self.set_license_server(primetime_data["licenseServerUrl"])
                break
            else:
                retry_count += 1
                time.sleep(0.2)

# attach to parent's pre-delete handler
from django.db.models.signals import pre_delete
pre_delete.connect(pre_delete_handler, sender=PrimetimeProvider)
