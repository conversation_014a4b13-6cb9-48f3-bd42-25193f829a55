# pylint: disable=E1101,E1120,E1123
from __future__ import division
# Django packages
from django.db import models, IntegrityError, DatabaseError, transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.db.models.signals import post_save

# System
import re, datetime, math

# keystore_app modules
from .. import settings, logger
from .precache import PreCachedKeys
from pysrc.keystore_app.models.keys import Key, KeyUsage


def validate_keyconfig_name(value):
    """
    Utility function to validate data entered on the forms.  This one is used to
    ensure that the keyconfig name is a valid format.  If it isn't valid, it throws an
    exception which gets displayed on the admin page.
    :param value - The value entered by the user.
    """
    from ..urls import CONFIG_PATTERN
    if value.find(' ') != -1:
        raise ValidationError("'%(value)s is not a valid config name, no spaces allowed", params={'value': value})

    regex = re.compile('^' + CONFIG_PATTERN + '$')
    match = regex.match(value)
    if not match:
        raise ValidationError("'%(value)s is not a valid config name, no symbols allowed except underscores",
                              params={'value': value})
    if match.start() != 0 or match.end() != len(value):
        raise ValidationError("'%(value)s is not a valid config name, no symbols allowed except underscores",
                              params={'value': value})


def validate_seg_size(value):
    """
    Utility function to validate the segment size entered on the forms.  It must
    be a positive number.
    :param value - The value entered by the user.
    """
    if not isinstance(value, int) or (value <= 0):
        raise ValidationError('the segment size must be a positive integer')


class KeyConfig(models.Model):
    """
    This class is used to record a pre-configured, named, group of settings.  When
    the user asks for a key, they also specify a 'name', which corresponds to a
    instance of this class, which indicates how to configure the stream (and keys),
    if the keys don't already exist.

    The notion of a 'user' key has been removed.  It was really only meant to be used by the RSDVR,
    and for that, what it was decided to do was a create a keyconfig with a short lifetime, a long
    rotation time, and when requesting a key, use the User's ID/guid in place of the stream guid.
    This will in essense create a 'user specific' key.
    """
    LIVE_KEY = 'live'
    VOD_KEY = 'vod'
    KEY_CONFIG_USAGE_CHOICES = (
        (VOD_KEY, 'VoD'),
        (LIVE_KEY, 'Live'),
    )

    # the values that go into the database
    SEGMENT_ROT = 'segments'
    TIME_ROT = 'time'

    # the choices we present to the user
    KEY_ROTATION_CHOICES = (
        (SEGMENT_ROT, 'Segment Rotation'),
        (TIME_ROT, 'Time Rotation'),
    )

    name = models.CharField('Config Name', max_length=30, primary_key=True, validators=[validate_keyconfig_name])
    usage = models.CharField(max_length=20, choices=KEY_CONFIG_USAGE_CHOICES, default=LIVE_KEY)
    lifetime = models.PositiveIntegerField('Key Lifetime (hrs)', blank=True, null=True)
    band_id = models.CharField(max_length=20, blank=True)
    rotation_method = models.CharField(max_length=20, choices=KEY_ROTATION_CHOICES, default=SEGMENT_ROT)
    seg_size = models.PositiveIntegerField('Rotation segment size', default=settings.segment_size,
                                           validators=[validate_seg_size])
    rotation_time = models.TimeField(default=datetime.time(hour=3))  # default to 3am
    pre_create_keys = models.PositiveIntegerField("Keys to create before needed", default=5)
    notes = models.CharField("Notes", max_length=200, help_text="Free-form description of KeyConfig", blank=True)

    class Meta(object):
        """ Used to tell django which app this model belongs to. """
        app_label = 'keystore_app'

    def to_dict(self):
        """
        Simple method to get a json version of this key config.
        @return A dictionary representation of this keyconfig, suitable for printing
        """
        result = {
                "name": self.name,
                "usage": self.usage,
                "lifetime": self.lifetime,
                "band_id": self.band_id if self.is_live() else "------"
                }
        if self.rotation_method == KeyConfig.SEGMENT_ROT:
            result["rotation_size"] = self.seg_size
        else:
            result["rotation_time"] = self.rotation_time
        return result

    def num_providers(self):
        """ Simple method to get the total number of providers for this config. """
        return self.keyconfigrelation_set.count()

    def all_providers(self):
        """
        Method used to get a list of all the providers for this config.  Starts being more
        useful as more different providers are added to the system.
        :return A list of all the providers for this config.
        """
        result = []
        for rel in self.keyconfigrelation_set.all():
            result.append(rel.content_object)
        return result


    def generate_range_usage(self, stream, start, end):
        """
        Used to generate keys for a given stream, for the given segments, using this KeyConfig as the
        configuration settings.  If all the required segments already exist, this method won't actually
        do anything.
        :param stream - The stream being worked on.
        :param start - The start segment for the stream.
        :param end - The end segment for the stream.
        :return Whether all the keys were generated or not.
        """
        existing_usages = KeyUsage.objects.filter(stream_id=stream.id)
        new_keys = 0

        # get the first segment inside the key where the 'start' segment falls.  For example, if seg_size
        # is 1800 and start is 2601, make temp 1801
        temp = (start // self.seg_size) * self.seg_size + 1

        while temp <= end and new_keys < settings.max_range_generation:
            if not existing_usages.filter(startSeg__lte=temp, endSeg__gte=temp).exists():
                self.generate_single_usage(stream, temp)
                new_keys += 1
            temp += stream.encryption_block_size
        return temp > end  # why it finished the loop

    def generate_single_usage(self, stream, segment):
        """
        Used to generate keys for a given stream, for the given segment, using this KeyConfig as the
        configuration settings.  It looks at the rotation method in this KeyConfig, and hands processing
        off to the appropriate helper function.
        :param stream - The stream being worked on.
        :param segment - The segment to generate a key for.
        """
        if self.rotation_method == KeyConfig.TIME_ROT:
            self._gen_single_timed_usage(stream, segment)
        else:
            self._gen_single_seg_usage(stream, segment)

    # Making this function atomic, as we are writing to 2 tables 'key' and 'key_usage' table, and writing in just
    # one of the 2 could cause inconsitency. If writing to 'key_usage' fails, the transaction will roll back.
    # Earlier, there were instances when 2 requests coming parallely would both create entries in 'key' table, but 
    # of the requests will fail on 'key_usage' table, because of unique key constaints(stream_id and start_seg).
    @transaction.atomic
    def _gen_single_seg_usage(self, stream, segment):
        assert self.rotation_method == KeyConfig.SEGMENT_ROT, 'Called _gen_single_seg_usage on wrong KeyConfig'
        block_start = 1
        while not block_start <= segment <= (block_start + stream.encryption_block_size - 1):
            block_start += stream.encryption_block_size

        # at this point it assumes the caller has made sure a key usage didn't already exist for this segment
        if self.usage == KeyConfig.LIVE_KEY and self.pre_create_keys > 0:
            pre_cache, _ = PreCachedKeys.objects.get_or_create(key_config=self, content_id=stream.content_id)
            key = pre_cache.get_unused_key()
        else:
            key = Key.create(self, stream.content_id)

        try:
            # if this is done inside a higher transaction, and an exception occurs, it prevents the higher
            # transaction from future database accesses.  If this becomes a problem, this KeyUsage creation
            # can be placed in a nested transaction
            KeyUsage.objects.create(key=key, stream=stream, startSeg=block_start,
                    endSeg=block_start + stream.encryption_block_size - 1)
        except (IntegrityError, DatabaseError):  # pylint: disable=catching-non-exception
            # thrown if unique_together on KeyUsage already exists
            logger.warning("KeyUsage already exists")
            raise          # Re-raise the exception for the caller function to respond with error
            
    @transaction.atomic
    def _gen_single_timed_usage(self, stream, segment):
        """
        Used to generate keys for a given stream, for the given segment, using this KeyConfig as the
        configuration settings.  It assumes the caller has already check to make sure a KeyUsage covering
        the given segment doesn't already exist.
        :param stream - The stream being worked on.
        :param segment - The segment to generate a key for.
        """
        assert self.rotation_method == KeyConfig.TIME_ROT, 'Called _gen_single_timed_usage on wrong KeyConfig'
        segments_per_key = int(math.floor(24 * 60 * 60 / (stream.segment_duration / 1000.0)))

        # you can't directly subtract 2 'time' values, they must have a date associated with it first
        # so this ugly logic is used to find the amount of time from the first segment, to when the first
        # rotation will occur
        now = timezone.now()
        rot_date = datetime.datetime.combine(now, self.rotation_time)
        create_date = datetime.datetime.combine(now, stream.create_time.time())
        if stream.create_time.time() >= self.rotation_time:
            rot_date +=  datetime.timedelta(days=1)
        first_rot_delay = rot_date - create_date

        # now that we know how long until the first rotation, we can calculate how many segments
        # the first key will cover
        first_key_block_size = int(math.floor(first_rot_delay.total_seconds() / (stream.segment_duration / 1000.0)))

        # now determine which key block the request segment falls in
        block_start = 1
        if not block_start <= segment < (block_start + first_key_block_size):
            block_start += first_key_block_size
        while not block_start <= segment < (block_start + segments_per_key):
            block_start += segments_per_key

        # at this point it assumes the caller has made sure a key usage didn't already exist for this segment
        if self.usage == KeyConfig.LIVE_KEY and self.pre_create_keys > 0:
            pre_cache, _ = PreCachedKeys.objects.get_or_create(key_config=self, content_id=stream.content_id)
            key = pre_cache.get_unused_key()
        else:
            key = Key.create(self, stream.content_id)

        try:
            # if this is done inside a higher transaction, and an exception occurs, it prevents the higher
            # transaction from future database accesses.  If this becomes a problem, this KeyUsage creation
            # can be placed in a nested transaction
            end_seg = first_key_block_size if block_start == 1 else block_start + segments_per_key - 1
            KeyUsage.objects.create(key=key, stream=stream, startSeg=block_start, endSeg=end_seg)
        except (IntegrityError, DatabaseError):  # pylint: disable=catching-non-exception
            # thrown if unique_together on KeyUsage already exists
            logger.warning("KeyUsage already exists")
            raise

    def __str__(self):
        """ Used to display the name of the config on the admin page. """
        return self.name

    def is_live(self):
        """ Simple function to see if this KeyConfig is for live content or not. """
        return self.usage == KeyConfig.LIVE_KEY

    @staticmethod
    def valid_name(value):
        """
        Method to check if a value is a valid KeyConfig name.
        :param value - The value to test if its a valid name.
        :return True/False if the parameter is a valid name.
        """
        try:
            validate_keyconfig_name(value)
        except ValidationError:
            return False
        return True

    def save(self, *args, **kwargs):
        """
        Customized save to only allow KeyConfigs to be saved if the assigned 'usage' is a valid option.
        This isn't really an issue when using the web UI, it's more relevant for the unit tests.
        """
        for x in KeyConfig.KEY_CONFIG_USAGE_CHOICES:
            if self.usage in x:
                super(KeyConfig, self).save(*args, **kwargs)
                break
        else:
            raise ValidationError("Invalid KeyConfig usage: %s" % self.usage)


class KeyConfigRelation(models.Model):
    key_config = models.ForeignKey(KeyConfig, on_delete=models.CASCADE)

    # these 3 fields together allow a KeyConfigRelation to be tied to any type of provider
    from .providers import Provider
    content_type = models.ForeignKey(ContentType, verbose_name="Provider Type",
            limit_choices_to=Provider.child_classes_query_set, on_delete=models.CASCADE)
    object_id = models.CharField("Provider Name", max_length=30)
    content_object = GenericForeignKey("content_type", "object_id")  # pylint: disable=E1120

    class Meta(object):
        """
        Used to tell django which app this model belongs to, as well as ensuring that a KeyConfig has only
        1 instance of a provider (can't put the same provider in multiple times).
        """
        app_label = 'keystore_app'
        unique_together = ["key_config", "content_type", "object_id"]


def keyconfig_cleanup_metadata(instance_name):
    """
    Method used to fire off an async task on a save of an existing key config.  Originally the async task
    was done here, but if the database was large, it would take some time, and the web UI wouldn't return
    until this function finished, and eventually time out.  With an async task, the UI can return instantly,
    and the job can still be done.
    @param instance_name - The id of the instance of KeyConfig just saved.
    """
    # this import must be done late, otherwise the django automatic import thingy fouls up.
    from pysrc.keystore_app.tasks.local_tasks import reset_key_metadata
    reset_key_metadata.apply_async([instance_name], countdown=5)  # execute in 5 seconds


def keyconfig_post_save_signal_handler(instance, created, raw, **kwargs):  # pylint: disable=unused-argument
    """
    Method used to receive the post_save signal from django and farm out the work.
    Separated here from keyconfig_cleanup_metadata so that the celery call can be mocked away for testing.
    @param instance - The instance of KeyConfig just saved.
    @param created - Whether it was a new instance (true) or just updated (false).
    @param raw - Whether it was loaded from a fixture or not. (not used)
    @param kwargs - (not used)
    """
    if not created:
        keyconfig_cleanup_metadata(instance.name)

# signal handlers
post_save.connect(keyconfig_post_save_signal_handler, sender=KeyConfig)
