[{"fields": {"bands_url": null, "url": null}, "model": "keystore_app.securemediaprovider", "pk": "sm_default"}, {"fields": {"url": "http://10.84.86.249:8180/cks-ws-keyAndSignalization/key"}, "model": "keystore_app.nagraprovider", "pk": "ng_beta"}, {"fields": {"url": null}, "model": "keystore_app.nagraprovider", "pk": "ng_default"}, {"fields": {"url": null}, "model": "keystore_app.internalprovider", "pk": "int_default"}, {"fields": {"url": null}, "model": "keystore_app.playreadyprovider", "pk": "pr_default"}, {"fields": {"provider": "jeremy", "url": null}, "model": "keystore_app.widevineprovider", "pk": "wv_default"}, {"fields": {"band_id": "devenc009", "lifetime": 24, "notes": "all current providers", "pre_create_keys": 0, "rotation_method": "segments", "rotation_time": "03:00:00", "seg_size": 1800, "usage": "live"}, "model": "keystore_app.keyconfig", "pk": "all_prov"}, {"fields": {"band_id": "devenc009", "lifetime": 1, "notes": "This is a sample note.", "pre_create_keys": 5, "rotation_method": "segments", "rotation_time": "03:00:00", "seg_size": 1800, "usage": "live"}, "model": "keystore_app.keyconfig", "pk": "load_testing"}, {"fields": {"band_id": "devenc009", "lifetime": null, "notes": "", "pre_create_keys": 0, "rotation_method": "segments", "rotation_time": "03:00:00", "seg_size": 43200, "usage": "vod"}, "model": "keystore_app.keyconfig", "pk": "test_vod"}]