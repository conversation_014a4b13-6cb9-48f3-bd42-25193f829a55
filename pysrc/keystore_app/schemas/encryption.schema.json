{"$schema": "http://json-schema.org/draft-04/schema#", "definitions": {"Key": {"description": "An Encryption Key", "properties": {"algorithm": {"description": "Encryption algorithm", "enum": ["AES_128_CBC"]}, "end_seg": {"description": "Last segment encrypted with this key", "type": "integer", "minimum": 0}, "expires": {"description": "The time when the key expires", "type": "string", "pattern": "^([0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2})$"}, "key": {"description": "Hex representation of the encryption key", "type": "string"}, "key_id": {"description": "key's id (UUID)", "$ref": "#/definitions/UUIDString"}, "providers": {"items": {"description": "Providers", "oneOf": [{"$ref": "#/definitions/SecureMediaProvider"}, {"$ref": "#/definitions/NagraProvider"}, {"$ref": "#/definitions/InternalProvider"}, {"$ref": "#/definitions/PlayReadyProvider"}, {"$ref": "#/definitions/WidevineProvider"}, {"$ref": "#/definitions/FairPlayProvider"}, {"$ref": "#/definitions/PrimetimeProvider"}]}, "type": "array"}, "start_seg": {"description": "First Segment encrypted with this key", "type": "integer", "minimum": 0}}, "required": ["algorithm", "key_id", "key", "start_seg", "end_seg", "providers"], "additionalProperties": false, "type": "object"}, "NagraProvider": {"description": "DRM Key Provider for Nagra", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "drm_name": {"description": "The DRM name", "type": "string"}, "drm_sys_id": {"description": "The DRM system ID, defined as a UUID", "$ref": "#/definitions/UUIDString"}, "type": {"description": "Provider Type", "enum": ["nagra"]}, "key_uri": {"description": "A URI of where the key can be obtained", "type": "string"}}, "required": ["name", "drm_sys_id", "drm_name", "type", "key_uri"], "additionalProperties": false, "type": "object"}, "SecureMediaProvider": {"description": "DRM Key Provider for SecureMedia", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "mkid": {"description": "Media Key ID", "type": "string"}, "pcw": {"description": "PCW (Protected Control Word)", "type": "string"}, "type": {"description": "Provider Type", "enum": ["securemedia"]}, "key_uri": {"description": "A URI of where the key can be obtained", "type": "string"}}, "required": ["name", "mkid", "pcw", "type", "key_uri"], "additionalProperties": false, "type": "object"}, "InternalProvider": {"description": "DRM Key Provider for Internal Access", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "type": {"description": "Provider Type", "enum": ["internal"]}, "key_uri": {"description": "A URI of where the key can be obtained", "type": "string", "pattern": "^http://([0-9a-zA-Z.-]+)/key/raw/([0-9a-fA-F]{32})$"}, "scheme_id_uri": {"description": "The DASH schemeIdUri for the mpd to indicate content has Clear Key support", "enum": ["urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b"]}, "pssh": {"description": "The PSSH to insert into the content header that client uses when using Clear Key", "type": "string", "maxLength": 0}}, "required": ["name", "type", "key_uri", "scheme_id_uri", "pssh"], "additionalProperties": false, "type": "object"}, "PlayReadyProvider": {"description": "DRM Key Provider for PlayReady", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "type": {"description": "Provider Type", "enum": ["playready"]}, "scheme_id_uri": {"description": "The dash schemeIdUri for the mpd to indicate content has playready support", "enum": ["urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95"]}, "pssh": {"description": "The PSSH to insert into the content header that client uses when talking to PlayReady server", "type": "string"}}, "required": ["name", "type", "pssh", "scheme_id_uri"], "type": "object"}, "WidevineProvider": {"description": "DRM Key Provider for Widevine", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "type": {"description": "Provider Type", "enum": ["widevine"]}, "proxy_url": {"description": "A url of where the Widevine proxy lives that clients should hit", "type": "string"}, "scheme_id_uri": {"description": "The dash schemeIdUri for the mpd to indicate content has widevine support", "type": "string", "pattern": "^urn:uuid:[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$"}, "pssh": {"description": "The PSSH to insert into the content header that client uses when talking to Widevine proxy", "type": "string"}}, "required": ["name", "type", "proxy_url", "scheme_id_uri", "pssh"], "additionalProperties": false, "type": "object"}, "FairPlayProvider": {"description": "DRM Key Provider for FairPlay", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "type": {"description": "Provider Type", "enum": ["fairplay"]}, "cert_uri": {"description": "a url of where to obtain the certificate to talk to the fairplay server", "type": "string", "pattern": "^http://.*/fairplay/certificate$"}, "key_uri": {"description": "The uri to hit on the fairplay server to get the CKC for this key", "type": "string", "pattern": "^skd://.*/fairplay/[0-9a-fA-F]{32}$"}, "key_format": {"description": "The KEYFORMAT value to go into the m3u8", "enum": ["com.apple.streamingkeydelivery"]}, "key_format_version": {"description": "The KEYFORMAT-VERSION value to go into the m3u8", "type": "integer", "minimum": 1, "maximum": 1}}, "required": ["name", "type", "key_uri", "key_format", "key_format_version"], "additionalProperties": false, "type": "object"}, "PrimetimeProvider": {"description": "DRM Key Provider for Primetime", "properties": {"name": {"description": "The name of the provider, as defined by the keystore admin", "type": "string"}, "type": {"description": "Provider Type", "enum": ["primetime"]}, "faxs_cm": {"description": "Base64 encoded value obtained from Comcast servers", "type": "string", "pattern": "^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$"}, "faxs_cm_hash": {"description": "SHA1 hash of the decoded faxs_cm value", "type": "string", "pattern": "^([a-f0-9]{40})$"}, "key_uri": {"description": "A URI of where the key can be obtained", "type": "string"}}, "required": ["name", "type", "faxs_cm", "faxs_cm_hash", "key_uri"], "additionalProperties": false, "type": "object"}, "UUIDString": {"description": "A string with canonical UUID formatting", "type": "string", "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})|([0-9a-fA-F]{32})$"}}, "description": "Stream Encryption Schema", "id": "http://schema.movenetworks.com/qmx/streamencryption/schema#", "properties": {"keys": {"items": {"$ref": "#/definitions/Key", "description": "Providers"}, "minItems": 1, "type": "array"}, "stream_id": {"description": "Stream's id (UUID)", "$ref": "#/definitions/UUIDString"}, "media_id": {"description": "The content ID/band ID for the key", "type": "string"}, "media_type": {"description": "The type of stream the key is used for", "enum": ["live", "vod", "user"]}, "encryption_block_size": {"description": "How many segments each encryption key covers", "type": "integer", "minimum": 1}, "rotation_time": {"description": "The time during the day (UTC) that key rotation takes place", "type": "string", "pattern": "^[0-9]{2}:[0-9]{2}:[0-9]{2}$"}}, "required": ["stream_id", "keys", "media_type", "media_id"], "additionalProperties": false, "title": "StreamEncryption", "type": "object"}