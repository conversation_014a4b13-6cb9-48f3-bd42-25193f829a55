# pylint: disable=W0613,E1120,E1123,broad-except
from __future__ import division
# Django packages
from django.http import HttpResponse, HttpResponseRedirect
from django.template import loader
from django.contrib.auth.decorators import user_passes_test
from django.db import transaction, IntegrityError, DatabaseError
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone

# System packages
import json, uuid, re, os, datetime, pytz, sys, binascii, math

# Keystore packages
from . import settings, logger
from pysrc.keystore_app.models import Key, KeyConfig, KeyUsage, Stream, NoPreviousKeyException
from pysrc.keystore_app.models.securemedia.kvhls import SecureMediaProvider, KVHLSException
from pysrc.keystore_app.models.nagra import NagraProvider, NagraException
from pysrc.keystore_app.models.playready import PlayReadyProvider, PlayReadyException

JSON_TYPE = "application/json"  # string constants so we don't fat-finger it all over the place


def conf_file_authorized(user):
    """
    Method used to test if a user is allowed to access restricted calls in the keystore.  If
    authentication is on in the config file, it makes sure the user is authenticated.  If it's
    off in the config file, they can access anything.
    :param user - The user to check if they are authorized.
    """
    if not settings.require_authentication:
        return True
    return user.is_authenticated()


def create_error_rsp(error_msg, status_code):
    """
    Helper method for generating properly formatted error messages to send back to the client.
    :param error_msg - The error message to send back, a string.
    :param status_code - The status code to send back.
    """
    result = {'error': error_msg}
    response = HttpResponse(json.dumps(result), content_type=JSON_TYPE)
    response.status_code = status_code
    if status_code >= 500:
        logger.error("status %r: %r", status_code, error_msg)
        response["Cache-Control"] = "public, max-age=5"
    else:
        logger.warning("status %r: %r", status_code, error_msg)
        response["Cache-Control"] = "public, max-age=3600"  # hour
    return response


def create_generic_rsp(json_msg, status_code, cache_header=False):
    """
    Helper method for generating properly formatted messages to send back to the client.
    :param json_msg - The message to send back, in json.
    :param status_code - The status code to send back.
    :param cache_header - Whether a cache header should be attached or not.  The max-age is
        based on the 'status_code' passed in.
    """
    response = HttpResponse(json.dumps(json_msg), content_type=JSON_TYPE)
    if cache_header:
        if status_code == 202:
            response["Cache-Control"] = "public, max-age=5"
        else:
            # find out when the earliest key expires, and set the Cache-Control to that
            min_expires = datetime.datetime.now() + datetime.timedelta(weeks=4)
            for usage in json_msg.get("keys", {}):
                key_expires = datetime.datetime.strptime(usage["expires"], "%Y-%m-%d %H:%M:%S")
                if key_expires < min_expires:
                    min_expires = key_expires
            # handle case where there is only 1 key, like a /key/info/<guid> request
            if "key" in json_msg:
                key_expires = datetime.datetime.strptime(json_msg["expires"], "%Y-%m-%d %H:%M:%S")
                if key_expires < min_expires:
                    min_expires = key_expires
            expire_seconds = (min_expires - datetime.datetime.now()).total_seconds()
            response["Cache-Control"] = "public, max-age=%i" % expire_seconds
    response.status_code = status_code
    return response


def status(_request):
    result = {}
    providers = {}
    TIMEOUT = 2
    for key_conf in KeyConfig.objects.all():
        result[key_conf.name] = key_conf.to_dict()
        result[key_conf.name]["providers"] = []

        # get rotation time (if present) in a string format
        if "rotation_time" in result[key_conf.name]:
            result[key_conf.name]["rotation_time"] = result[key_conf.name]["rotation_time"].isoformat()

        for prov in key_conf.all_providers():
            if prov.name in providers:
                result[key_conf.name]["providers"].append(providers[prov.name])
            else:
                try:
                    url = prov.get_url()
                    if prov.check_connection(TIMEOUT, url):
                        providers[prov.name] = {
                                "name": prov.name,
                                "type": prov.get_type(),
                                "status": "OK",
                                "healthy": True,
                                "url": url
                                }
                    # special case for SecureMediaProvider, can we get band IDs?
                    if isinstance(prov, SecureMediaProvider):
                        bands = prov.update_band_names(TIMEOUT)
                        if not bands:
                            providers[prov.name]["message"] = "Failed to get band IDs"
                            providers[prov.name]["status"] = "FAILED"
                            providers[prov.name]["healthy"] = False
                except Exception as ex:
                    providers[prov.name] = {
                            "name": prov.name,
                            "type": prov.get_type(),
                            "status": "FAILED",
                            "healthy": False,
                            "url": url,
                            "message": str(ex)
                            }
                result[key_conf.name]["providers"].append(providers[prov.name])
    return create_generic_rsp(result, 200)


@user_passes_test(conf_file_authorized)
def get_rsdvr_keys(request, config, user_guid):
    """
    This endpoint is used by the rsdvr for getting keys.  The big difference for them is that the stream guid
    and user guid are the same (so only pass in 1 of them).  They also don't care about segment numbers, so
    don't need to worry about that either.  They are interested in Key expiration time.  So when a request comes
    in, if a key exists for the user, and the key hasn't expired, send back all the keys for that user.  If all
    their keys have expired, generate a new one to include in the list sent back.
    :param config - the name of the key config to use if the Keys aren't found.
    :param user_guid - the ID of the user to get the key for.
    """
    logger.debug("saw request: config=%s, user_guid=%s", config, user_guid)
    user_guid = uuid.UUID(user_guid)
    result_code = 200
    result = {
        "stream_id": str(user_guid),
        "keys": [],
    }

    try:
        key_conf = KeyConfig.objects.get(pk=config)

        # ignore 'block_num' param for non-live content
        if not key_conf.is_live():
            return create_error_rsp("RSDVR KeyConfigs need to be Live, not VoD", 500)

        # ensure we have a KeyUsage for the stream, and a stream to use, sorted by newest usage first
        try:
            usage = KeyUsage.objects.get(stream_id=user_guid)
        except KeyUsage.DoesNotExist:
            try:
                stream, _ = Stream.objects.get_or_create(pk=user_guid, defaults={
                    "content_id": "rsdvr",
                    "encryption_block_size": 85000  # rsdvr keys are always 85000 segments
                })
                key_conf.generate_single_usage(stream, 1)
                usage = KeyUsage.objects.get(stream_id=user_guid)
            except IntegrityError:  # pylint: disable=catching-non-exception
                # occurs if we get a thread switch at the right time, and someone else beat this thread to
                # creating the same stream.  Less likely to occur after moving to the get_or_create
                return create_error_rsp("Race condition creating stream %s" % user_guid, 500)

        if usage.key.has_expired:
            usage.key.delete()
            usage.stream.delete()
            usage.delete()
            return get_rsdvr_keys(request, config, user_guid.hex)

        #usage.key.just_accessed()
        result["keys"].append(usage.to_dict())
        result["media_id"] = usage.media_id
        result["media_type"] = usage.media_type
        result["encryption_block_size"] = usage.stream.encryption_block_size
        if not usage.key.has_all_meta:
            result_code = 202

        # sometimes, for some unknown reason, the key might be empty
        if not result["keys"]:
            logger.exception("Blank key returned")
            return create_error_rsp("A blank key was returned, try again", 500)
    except IntegrityError:  # pylint: disable=catching-non-exception
        logger.exception("IntegrityError in get_rsdvr_keys")
        return create_error_rsp("KeyUsage integrity violation, try again", 500)
    except DatabaseError:  # pylint: disable=E0712
        logger.exception("DatabaseError in get_rsdvr_keys")
        return create_error_rsp("Failed to get a lock on stream object", 500)
    except KeyConfig.DoesNotExist:
        return create_error_rsp("No Key Config found", 404)
    except NoPreviousKeyException as e:
        logger.exception(e.str())
        return create_error_rsp("Providers not responding, no previous key available", 503)
    return create_generic_rsp(result, result_code, True)


@user_passes_test(conf_file_authorized)
def get_block(request, config, content, stream, block_num):
    """
    This method is where user requests for key blocks enter the keystore.  A 'block' is a specific KeyUsage
    range on a stream, e.g., block 1 is for a KeyUsage with startSeg=1 and endSeg=encryptionkey_block_size.
    It will only ever return a single key, but does so in a 'keys' array, so the output is identical to the
    /key/get endpoint.
    :param config - the name of the key config to use if the Keys aren't found.
    :param content - the content ID for the stream
    :param stream - the ID of the stream the user wants the segment(s) for.
    :param block_num - The number of the block to get the keys for.  Must be 0 < block_num <= 1000.
    """
    block_num = int(block_num)
    logger.debug("saw request: config=%s, content_id=%s, stream=%s, block=%i", config, content, stream, block_num)
    stream_id = uuid.UUID(stream)
    result_code = 200
    result = {
        'stream_id': str(stream_id),
        'keys': [],  # will only ever have 1 entry
    }

    if block_num <= 0 or block_num > 1000:
        return create_error_rsp("Invalid block number, must be 0 < block_num <= 1000", 400)

    try:
        key_conf = KeyConfig.objects.get(pk=config)

        # ignore 'block_num' param for non-live content
        if not key_conf.is_live():
            block_num = 1

        # ensure we have a KeyUsage for the stream, and a stream to use
        usage = KeyUsage.objects.filter(stream_id=stream_id)
        if not usage.exists():
            try:
                stream, _ = Stream.objects.get_or_create(pk=stream_id, defaults={
                    "content_id": content,
                    "encryption_block_size": key_conf.seg_size
                })
            except IntegrityError:  # pylint: disable=catching-non-exception
                # occurs if we get a thread switch at the right time, and someone else beat this thread to
                # creating the same stream.  Less likely to occur after moving to the get_or_create
                return create_error_rsp("Race condition creating stream %s" % stream_id, 500)
        else:
            stream = usage[0].stream

        # if the keyconfig uses time rotation, verify query args from the request
        if key_conf.rotation_method == KeyConfig.TIME_ROT:
            create_time = request.GET.get("create_time")
            duration = request.GET.get('duration')
            if create_time is None or not create_time.isdigit():
                return create_error_rsp("Missing/malformed query args 'create_time' to use a time-rotation " \
                        "KeyConfig", 400)
            elif duration is None or not duration.isdigit():
                return create_error_rsp("Missing/malformed query args 'duration' to use a time-rotation KeyConfig", 400)
            elif int(duration) <= 0:
                return create_error_rsp("Invalid duration, must but be > 0", 400)
            elif int(create_time) < 0:
                return create_error_rsp("Invalid 'create_time', must but be >= 0", 400)

            # make sure the stream has a create_time and segment_duration, only set them once,
            # so they don't change, but first get a lock on the stream
            with transaction.atomic():
                stream = Stream.objects.select_for_update().get(pk=stream.pk)
                if stream.create_time is None:
                    stream.create_time = datetime.datetime.fromtimestamp(int(create_time), tz=pytz.utc)
                    stream.segment_duration = int(duration)
                    stream.save()
            result["rotation_time"] = str(key_conf.rotation_time)
            if block_num == 1:
                start_seg = 1
            else:
                # need to find how long until the first key rotation based on the stream's create time
                # and segment duration
                now = timezone.now()
                segments_per_key = int(math.floor(24 * 60 * 60 / (stream.segment_duration / 1000.0)))
                rot_date = datetime.datetime.combine(now, key_conf.rotation_time)
                create_date = datetime.datetime.combine(now, stream.create_time.time())
                if stream.create_time.time() >= key_conf.rotation_time:
                    rot_date += datetime.timedelta(days=1)
                first_rot_delay = rot_date - create_date

                # now that we know how long until the first rotation, we can calculate how many segments
                # the first key will cover
                first_key_block_size = int(math.floor(first_rot_delay.total_seconds() \
                        / (stream.segment_duration / 1000.0)))
                # now determine the first segment number in the block_num requested
                start_seg = 1 + first_key_block_size + (block_num - 1) * segments_per_key
        else:
            result["encryption_block_size"] = stream.encryption_block_size
            start_seg = (block_num - 1) * stream.encryption_block_size + 1

        single_use = usage.filter(startSeg=start_seg)
        if single_use.count() == 1:
            result["keys"].append(single_use[0].to_dict())
            single_use[0].key.just_accessed()
        elif single_use.count() == 0:
            # no usage exists, create one, which automatically gets put in the single_use querySet
            key_conf.generate_single_usage(stream, start_seg)
            result['keys'].append(single_use[0].to_dict())
        result["media_id"] = single_use[0].media_id
        result["media_type"] = single_use[0].media_type
        if not single_use[0].key.has_all_meta:
            result_code = 202

        # sometimes, for some unknown reason, the key might be empty
        if not result["keys"]:
            logger.exception("Blank key returned")
            return create_error_rsp("A blank key was returned, try again", 500)

    except IntegrityError:  # pylint: disable=catching-non-exception
        logger.exception("IntegrityError in get_block")
        return create_error_rsp("KeyUsage integrity violation, try again", 500)
    except DatabaseError:  # pylint: disable=E0712
        logger.exception("DatabaseError in get_block")
        return create_error_rsp("Failed to get a lock on stream object", 500)
    except KeyConfig.DoesNotExist:
        return create_error_rsp("No Key Config found", 404)
    except NoPreviousKeyException:
        return create_error_rsp("Providers not responding, no previous key available", 503)
    return create_generic_rsp(result, result_code, True)


@transaction.atomic
@csrf_exempt
@user_passes_test(conf_file_authorized)
def generate_bulk(request):
    """
    This method is where user requests for bulk keys enter the keystore.
    Bulk key request is a two part asynchronous call. The call to generate
    kicks off an async task via celery and returns a UUID to identify the request.

    The request must be a POST with a JSON payload containing two fields:
        "key_config": <string>,
        "num_keys": <int>
    """
    from pysrc.keystore_app.tasks.local_tasks import generate_bulk_keys
    try:
        req_dict = json.loads(request.body.decode(errors='ignore'))
        key_config = req_dict.get("key_config", None)
        try:
            num_keys = int(req_dict.get("num_keys", None))
        except Exception as ex:
            return create_error_rsp("Bad Request: num_keys not recognized", 400)
        if num_keys < 1:
            return create_error_rsp("Bad Request: num_keys must be greater then zero", 400)

        # Check if valid key config here so async task doesnt fail later
        key_conf = KeyConfig.objects.get(pk=key_config)

    except KeyConfig.DoesNotExist:
        return create_error_rsp("Key Config Not Found: {}".format(key_config), 404)
    except ValueError:
        return create_error_rsp("Bad Request: Malformed JSON", 400)
    except Exception as ex:
        return create_error_rsp(str(ex), 500)

    logger.debug("saw bulk request: key_config=%s, num_keys=%s", key_config, num_keys)

    result = generate_bulk_keys.delay(key_conf.name, num_keys)

    logger.info("Got bulk key generate request: key_config: %s, num_keys: %s, task_id: %s",
            key_config, num_keys, result.id)

    return create_generic_rsp(result.id, 200)


@user_passes_test(conf_file_authorized)
def get_bulk(request, bulk_request_id):
    """
    This method handles getting the keys from a bulk key request. This is the
    second half of the bulk key request, passing the bulk request id obtained
    from the first part returnes the generated keys, or a status message if the
    keys are not yet generated.
    :param - bulk_request_id UUID of the bulk key request
    """
    from pysrc.keystore_app.tasks.local_tasks import generate_bulk_keys
    task_result = generate_bulk_keys.AsyncResult(bulk_request_id)
    task_status = task_result.state

    # possible states:
    # "PENDING" | "STARTED" | "FAILURE" | "RETRY" | "REVOKED" | "SUCCESS"
    # http://docs.celeryproject.org/en/latest/userguide/tasks.html#built-in-states

    logger.info("Got bulk key get request: bulk_request_id: %s, task_status: %s", bulk_request_id, task_status)

    if task_status in ['PENDING',  'STARTED', 'RETRY', 'FAILURE', 'REVOKED']:
        response = {"status": task_status}
        return create_generic_rsp(response, 203)

    elif task_status == 'SUCCESS':
        keys = [Key.objects.get(pk=key_id) for key_id in task_result.result]
        response = {"keys": [{
            "key_id": key.id.hex,
            "key": binascii.hexlify(key.secret).decode(),
            "expires": key.expires(),
            "algorithm": key.algorithm,
            "providers": key.providers_meta
        } for key in keys]}
        return create_generic_rsp(response, 200)

    else:
        logger.error("Bulk key generation failure: bulk_request_id=%s, task_status=%s", bulk_request_id, task_status)
        return create_error_rsp("Bulk key generation failure", 500)


@user_passes_test(conf_file_authorized)
def get_key(request, config, content, stream, start, end):
    """
    This method is where user requests enter the keystore.  The user needs to have supplied the config name, stream ID,
    start segment, and optionally an end segment.  If the keys don't exist for the stream segment(s) desired, they are
    created using the settings in the KeyConfig.  This method operates as an atomic operation, so only 1 thread can
    call it at a time.  If a range request is done, only max_range_generation keys will be created.  In the event
    that not all the requested keys are returned, the response status code will be 202.
    :param config - the name of the key config to use if the Keys aren't found.
    :param content - the content ID for the stream
    :param stream - the ID of the stream the user wants the segment(s) for.
    :param start - the start segment of the stream.
    :param end - the optional end segment of the stream.
    """
    start = int(start)
    end = int(end) if end is not None else -1
    logger.debug("saw request: config=%s, content_id=%s, stream=%s, range=%i-%i", config, content, stream, start, end)
    stream_id = uuid.UUID(stream)
    result_code = 200
    result = {
        'stream_id': str(stream_id),
        'keys': [],
    }

    if start <= 0:
        return create_error_rsp("Invalid start segment value", 400)

    try:
        key_conf = KeyConfig.objects.get(pk=config)

        # ignore 'segment' params for non-live content
        if not key_conf.is_live():
            start = 1
            end = -1

        # ensure we have a KeyUsage for the stream, and a stream to use
        usage = KeyUsage.objects.filter(stream_id=stream_id)
        if not usage.exists():
            try:
                stream, _ = Stream.objects.get_or_create(pk=stream_id, defaults={
                    "content_id": content,
                    "encryption_block_size": key_conf.seg_size
                })
            except IntegrityError:  # pylint: disable=catching-non-exception
                # occurs if we get a thread switch at the right time, and someone else beat this thread to
                # creating the same stream.  Less likely to occur after moving to the get_or_create
                return create_error_rsp("Race condition creating stream %s" % stream_id, 500)
        else:
            stream = usage[0].stream

        # if the keyconfig uses time rotation, verify query args from the request
        if key_conf.rotation_method == KeyConfig.TIME_ROT:
            create_time = request.GET.get('create_time')
            duration = request.GET.get('duration')
            if create_time is None or not create_time.isdigit():
                return create_error_rsp("Missing/malformed query args 'create_time' to use a time-rotation " \
                        "KeyConfig", 400)
            elif duration is None or not duration.isdigit():
                return create_error_rsp("Missing/malformed query args 'duration' to use a time-rotation KeyConfig", 400)
            elif int(duration) <= 0:
                return create_error_rsp("Invalid duration, must but be > 0", 400)
            elif int(create_time) < 0:
                return create_error_rsp("Invalid 'create_time', must but be >= 0", 400)

            # make sure the stream has a create_time and segment_duration, only set them once,
            # so they don't change, but first get a lock on the stream
            with transaction.atomic():
                stream = Stream.objects.select_for_update().get(pk=stream.pk)
                if stream.create_time is None:
                    stream.create_time = datetime.datetime.fromtimestamp(int(create_time), tz=pytz.utc)
                    stream.segment_duration = int(duration)
                    stream.save()
            result['rotation_time'] = str(key_conf.rotation_time)
        else:
            result['encryption_block_size'] = stream.encryption_block_size

        if (end == -1) or (start == end):  # only a single segment being asked for
            single_use = usage.filter(startSeg__lte=start, endSeg__gte=start)
            if single_use.count() == 1:
                result['keys'].append(single_use[0].to_dict())
                single_use[0].key.just_accessed()
            elif single_use.count() == 0:
                # no usage exists, create one, which automatically gets put in the single_use querySet
                key_conf.generate_single_usage(stream, start)
                result['keys'].append(single_use[0].to_dict())
            else:
                return create_error_rsp("Segments overlap", 503)
            result['media_id'] = single_use[0].media_id
            result['media_type'] = single_use[0].media_type
            if not single_use[0].key.has_all_meta:
                result_code = 202
        elif end < start:
            return create_error_rsp("End segment before start segment", 400)
        else:  # need to find multiple segments
            if not key_conf.generate_range_usage(stream, start, end):
                result_code = 202
            seg_use = None
            for seg_use in usage.exclude(endSeg__lt=start).exclude(startSeg__gt=end).order_by('startSeg'):
                result['keys'].append(seg_use.to_dict())
                seg_use.key.just_accessed()
                if not seg_use.key.has_all_meta:
                    result_code = 202
            result['media_id'] = seg_use.media_id
            result['media_type'] = seg_use.media_type

            # sometimes, for some unknown reason, the key might be empty
            if not result["keys"]:
                logger.exception("Blank key returned")
                return create_error_rsp("A blank key was returned, try again", 500)

    except IntegrityError:  # pylint: disable=catching-non-exception
        logger.exception("IntegrityError in get_key")
        return create_error_rsp("KeyUsage integrity violation, try again", 500)
    except DatabaseError:  # pylint: disable=E0712
        logger.exception("DatabaseError in get_key")
        return create_error_rsp("Failed to get a lock on stream object", 500)
    except KeyConfig.DoesNotExist:
        return create_error_rsp("No Key Config found", 404)
    except NoPreviousKeyException:
        return create_error_rsp("Providers not responding, no previous key available", 503)
    return create_generic_rsp(result, result_code, True)


@user_passes_test(conf_file_authorized)
def raw_key_from_stream(request, stream, segment):
    """
    This method is where a raw key can be obtained.  Normally this is contained in a key file,
    described in the URI inside a m3u8 file.  However, while dynamux is being developed, we
    need a way to get raw keys without a m3u8, thus this function.  Ideally this function
    will be gone in a production system.
    :param stream - The stream that will be using the key
    :param segment - The segment for the designed stream in question.
    """
    stream_id = uuid.UUID(stream)
    usage = KeyUsage.objects.filter(stream_id=stream_id, startSeg__lte=segment, endSeg__gte=segment)
    if not usage.exists():
        return create_error_rsp("No Key found", 404)
    return HttpResponse(usage[0].key.secret)


@user_passes_test(conf_file_authorized)
def raw_key(request, key_id):
    """
    This method is where a raw key can be obtained.  Normally this is contained in a key file,
    described in the URI inside a m3u8 file.  However, while dynamux is being developed, we
    need a way to get raw keys without a m3u8, thus this function.  Ideally this function
    will be gone in a production system.
    :param key_id - the ID (uuid) of the key we want the raw key for.
    """
    key_id = uuid.UUID(key_id)  # from string to UUID
    try:
        full_key = Key.objects.get(pk=key_id)
    except Key.DoesNotExist:
        return create_error_rsp("No Key found", 404)
    return HttpResponse(full_key.secret)


@user_passes_test(conf_file_authorized)
def remove_key(request, key_id):
    """
    This method is used to explicitly remove a key from the keystore.
    :param key_id - the UUID identifying the key to be removed.
    """
    try:
        key_id = uuid.UUID(key_id)
        to_remove = Key.objects.get(pk=key_id)
        to_remove.delete()
    except Key.DoesNotExist:
        return create_error_rsp("No such key", 404)
    return create_generic_rsp({'status': 'success'}, 200)


@user_passes_test(conf_file_authorized)
def key_info(request, key_id):
    """
    This method is used to get info about the first KeyUsage for a particular key.
    :param key_id - the UUID identifying the key to be inspected.
    """
    result = {}
    try:
        key_id = uuid.UUID(key_id)
        key = Key.objects.get(pk=key_id)
        if key.keyusage_set.count() > 0:
            result = key.keyusage_set.first().to_dict()
            result["has_all_meta"] = key.has_all_meta
        else:
            return create_error_rsp("No KeyUsages for given key", 404)
    except Key.DoesNotExist:
        return create_error_rsp("No such key", 404)
    # we don't bother with a 202 if we don't have all the meta-info for the key here,
    # we just use a field in the response json
    return create_generic_rsp(result, 200, result["has_all_meta"])


@user_passes_test(conf_file_authorized)
def key_usage(request, key_id):
    """
    This method return information about the usage for a particular key.
    Returns raw key, content id
    :param key_id - the ID (uuid) of the key we want the raw key for.
    """
    key_id = uuid.UUID(key_id)  # from string to UUID
    result = {}
    try:
        full_key = Key.objects.get(pk=key_id)
    except Key.DoesNotExist:
        return create_error_rsp("No Key found", 404)

    key_usages = KeyUsage.objects.filter(key=key_id)

    content_ids = set()
    content_ids.add(full_key.content_id)
    for ku in key_usages:
        content_id = Stream.objects.get(id=ku.stream.id).content_id
        # TODO: version 2.10 We used to set the stream id and content id to the 
        # user guid for rsdvr keys. Now we set the content id to 'rsdvr'. This 
        # check is to handle keys created before that change. At some point in 
        # the future we can remove this after all those keys are expired.
        if content_id == ku.stream.id:
            content_id = "rsdvr"
        content_ids.add(content_id)

    result['raw_key'] = full_key.secret.hex()
    result['content_id'] = list(content_ids)
    return create_generic_rsp(result, 200)


def bad_url(request, config, content, stream, start):
    """
    This method is used to handle the case where the URL sent to the keystore is malformed.
    It is defined to take 3 args, one (or more) of them isn't formed correctly.  If they are
    correctly formed, control would hit get_key instead.
    :param config - the name of the key config to use if the Keys aren't found.
    :param content - the content ID for the stream.
    :param stream - the ID of the stream the user wants the segment(s) for.
    :param start - the start segment of the stream.
    """
    # check keyconfig name
    if not KeyConfig.valid_name(config):
        return create_error_rsp("Invalid key config name format", 400)

    if not Stream.valid_id(stream):
        return create_error_rsp("Invalid stream ID format", 400)

    from pysrc.keystore_app.urls import CONTENT_PATTERN
    regex = re.compile('^' + CONTENT_PATTERN + '$')
    match = regex.match(content)
    if not match:
        return create_error_rsp("Invalid content ID format", 400)

    try:
        # check start number
        start = int(start)
        if start < 0:
            raise ValueError
    except ValueError:
        return create_error_rsp("Invalid start segment format, must be positive number", 400)


def get_empty(request):
    """
    Method used to handle all the requests for pages that don't exist. Just sends back a
    JSON 404 message.
    """
    return create_error_rsp("Invalid request.", 404)


def check_securemedia(request, timeout=0):
    """
    Method used to check the connection to the secure media servers.  If no timeout value is supplied
    it will take the value from the config file.
    :param timeout - The max amount of time to (in seconds) for a response from the Secure Media server.
    """
    try:
        timeout = int(timeout)
        if timeout < 1 or timeout > 300:
            timeout = settings.kvhls_timeout

        if SecureMediaProvider.check_connection(timeout):
            sm = SecureMediaProvider(name="check")  # don't actually save this instance
            bands = sm.update_band_names(timeout)
            if bands:
                return create_generic_rsp({
                    "status": "OK",
                    "healthy": True,
                    "bands": bands
                    }, 200)
            else:
                return create_generic_rsp({
                    "status": "FAILED",
                    "healthy": False,
                    "message": "Failed to get band IDs"
                    }, 503)
    except KVHLSException as ex:
        logger.exception("check_securemedia() FAILED: %s", ex)
        return create_generic_rsp({"status": "FAILED", "healthy": False, "message": str(ex)}, 503)

    logger.error('check_securemedia() FAILED: Check of KVHLS Service Failed.')
    return create_generic_rsp({"status": "FAILED", "healthy": False, "message": "Check of KVHLS Service Failed."}, 417)


def check_nagra(request, timeout=0):
    """
    Method used to check the connection to the nagra servers.  If no timeout value is supplied
    it will take the value from the config file.
    :param timeout - The max amount of time to (in seconds) for a response from the Nagra server.
    """
    try:
        timeout = int(timeout)
        if timeout < 1 or timeout > 300:
            timeout = settings.nagra_timeout

        if NagraProvider.check_connection(timeout):
            return create_generic_rsp({"status": "OK", "healthy": True}, 200)
    except NagraException as ex:
        logger.exception("check_nagra() FAILED: %s", ex)
        return create_generic_rsp({"status": "FAILED", "healthy": False, "message": str(ex)}, 503)

    logger.error('check_nagra() FAILED: Heartbeat to Nagra Key and Signalization Server Failed.')
    return create_generic_rsp({"status": "FAILED", "healthy": False,
                               "message": "Heartbeat to Nagra Key and Signalization Server Failed."}, 417)


def check_playready(request, timeout=0):
    """"
    Method used to check the connection to the PlayReady servers.  If no timeout value is supplied
    it will take the value from the config file.
    :param timeout - The max amount of time (in seconds) for a response from the PlayReady server.
    """
    try:
        timeout = int(timeout)
        if timeout < 1 or timeout > 300:
            timeout = settings.playready_timeout

        if PlayReadyProvider.check_connection(timeout):
            return create_generic_rsp({"status": "OK", "healthy": True}, 200)
    except PlayReadyException as ex:
        logger.exception('check_playready() FAILED: %s', ex)
        return create_generic_rsp({"status": "FAILED", "healthy": False, "message": str(ex)}, 503)

    logger.error("check_playready() FAILED: Unable to talk to PlayReady server.")
    return create_generic_rsp(
            {
                "status": "FAILED",
                "healthy": False,
                "message": "Check of PlayReady server Failed."
            }, 417)


def check_keyconfig(request, config_name, timeout=10):
    """
    Method used to check the providers connections to all the providers associated with
    a particular key config.  If no timeout is supplied, it will use 10.
    :param config_name - The name of the key config to check the connection of its providers.
    :param timeout - The number of seconds to wait for connection timeouts.
    """
    try:
        timeout = int(timeout)
        key_conf = KeyConfig.objects.get(pk=config_name)
        if timeout < 1 or timeout > 300:
            timeout = 10

        result = {'providers': [], 'live': key_conf.is_live(), 'rotation': key_conf.seg_size}
        if key_conf.is_live():
            result['band_id'] = key_conf.band_id
        providers = key_conf.all_providers()
        response_code = 200
        if not providers:
            result['message'] = 'No providers for this key config'
        else:
            for prov in providers:
                try:
                    url = prov.get_url()
                    if prov.check_connection(timeout, url):
                        result['providers'].append({
                            'name': prov.name,
                            'type': prov.get_type(),
                            'status': 'OK',
                            'healthy': True,
                            'url': url
                        })
                    # special case for SecureMediaProvider, can we get band IDs?
                    if isinstance(prov, SecureMediaProvider):
                        bands = prov.update_band_names(timeout)
                        if not bands:
                            del result["providers"][-1]
                            raise Exception("Failed to get band IDs")
                except Exception as ex:
                    response_code = 412  # means a precondition failed, the provider working
                    result['providers'].append({
                        'name': prov.name,
                        'type': prov.get_type(),
                        'status': 'FAILED',
                        'healthy': False,
                        'url': url,
                        'message': str(ex)
                    })
        return create_generic_rsp(result, response_code)
    except KeyConfig.DoesNotExist:
        return create_error_rsp("No Key Config found", 404)


def get_keyconfigs(request):
    """ Method used to get a list of all the KeyConfig names in the keystore. """
    result = {'keyconfigs': []}
    for conf in KeyConfig.objects.all():
        result['keyconfigs'].append(conf.name)
    return create_generic_rsp(result, 200)


def serve_schemas(request):
    """
    Method used to get a list of all the schemas that are being served by this app.
    Unlike most other views, this one uses templates to create a HTML page as a
    response.
    """
    from pysrc.keystore_srv.settings import STATIC_ROOT

    dir_list = os.listdir(os.path.join(STATIC_ROOT, 'schemas'))
    schema_list = [item for item in dir_list if item.endswith('.schema.json')]  # remove non-schema files
    template = loader.get_template('schemas.html')
    context = {"schema_list": schema_list}
    return HttpResponse(template.render(context))


def serve_api(request):
    """
    Method used to get a list of all the API calls that are being served by this app.
    Unlike most other views, this one uses templates to create a HTML page as a
    response.
    """
    from pysrc.keystore_srv.urls import urlpatterns  # pylint: disable=import-outside-toplevel
    import django.contrib.admindocs.views as admin_view  # pylint: disable=import-outside-toplevel

    allow_regex = ['keyconfigs', 'key_docs', 'key', 'check']
    view_functions = admin_view.extract_views_from_urlpatterns(urlpatterns)
    view_dict = {}

    # remove non-app urls, and parse out params in comments into a list
    for view in view_functions:
        slash_index = view[1].find('/')
        url_front = view[1][0:slash_index]
        if url_front in allow_regex:
            clean_url = view[1].replace('^', '').replace('$', '').replace('?P', '')
            main_doc = view[0].__doc__
            if main_doc is None:
                view_dict[clean_url] = ["(No docs found)"]
            elif '@' not in main_doc:
                view_dict[clean_url] = [main_doc]
            else:  # have docs for params or returns
                pieces = main_doc.split('@')
                view_dict[clean_url] = pieces
    # remove any functions we don't want to list in the api
    hidden_functions = ["key/create_pr_test", r"keyconfigs/regenerate_metadata_for_keyconfig/(<kc_name>\w+)/"]
    for hide in hidden_functions:
        if hide in view_dict:
            del view_dict[hide]

    # remove docs from items that have duplicate doc in following item
    sorted_views = sorted(view_dict.items())
    doc = sorted_views[0][1]
    for idx, val in enumerate(sorted_views):
        if idx == 0:
            continue
        if len(val) > 1:
            if val[1] == doc and doc[0] != 'No docs found':
                sorted_views[idx - 1] = (sorted_views[idx - 1][0], "")
            else:
                doc = val[1]
        else:
            doc = ""

    # send list to template for web page generation
    template = loader.get_template('api.html')
    context = {"function_dict": sorted_views}
    return HttpResponse(template.render(context))

def version(request):
    """
    Method used to get the current version of the keystore.  It looks for the version.json file
    stored in the pyenv/etc directory.  It formats the contents into a more human-readable form
    and returns it.  If the file is missing, it gives back a 500.
    """
    version_file_path = os.path.join(os.path.abspath(sys.prefix), 'etc', 'version.json')
    if os.path.exists(version_file_path):
        result = {}
        try:
            with open(version_file_path, 'r') as version_file:  # assumes file is formatted correctly
                contents = json.loads(version_file.read())
                result['major'] = contents[0]
                result['minor'] = contents[1]
                result['revision'] = contents[2]
                modified_time = os.path.getmtime(version_file_path)
                # date needs to be in YYYY-MM-DD format, as defined in schema
                result['date'] = datetime.datetime.utcfromtimestamp(modified_time).strftime("%Y-%m-%d")
        except IOError:
            return create_error_rsp("Error reading version file", 500)
        except IndexError:
            return create_error_rsp("Badly formatted version file", 500)
        return create_generic_rsp(result, 200)
    return create_error_rsp("Missing version file", 500)


def health(request):
    """
    Method used to check the health of the keystore.  It sends a json response of {healthy： true}
    as long as the server is running.
    """
    result = {}
    result['healthy'] = True
    return create_generic_rsp(result, 200)

def ready(request):
    """
    Return ready state of the keystore. If keystore is healthy and ready to process
    requests return {ready: true}, else {ready: false} with a discription of error status.
    """
    from django.db import connection
    from pysrc.keystore_srv.celerysetup import app

    result = {'ready': True}
    status_code = 200

    try:
        connection.ensure_connection()
        result['database'] = 'ok'
    except Exception as e:
        result['ready'] = False
        result['database'] = 'Unable to connect to database, see log for details'
        logger.error("Ready handler unable to connect to database: %r", e)
        status_code = 503

    try:
        celery_inspect = app.control.inspect()
        availability = celery_inspect.ping()

        if availability is not None:
            k = list(availability.keys())[0]
            if availability[k]['ok'] == 'pong':
                result['celery'] = 'ok'
            else:
                result['ready'] = False
                result['celery'] = 'Celery unavaliable, see log for details'
                logger.error("Ready handler got bad celery ping response: %r", availability)
                status_code = 503
        else:
            result['ready'] = False
            result['celery'] = 'Celery unavaliable, see log for details'
            logger.error("Ready handler unable to ping celery, got None response")
            status_code = 503
    except Exception as e:
        result['ready'] = False
        result['celery'] = 'Celery error, see log for details'
        logger.error("Ready handler celery error: %r", e)
        status_code = 503

    return create_generic_rsp(result, status_code)

def keystore_login(request):
    from django.contrib.auth import authenticate, login

    req_dict = None
    if request.method == 'POST':
        req_dict = request.POST
    elif request.method == 'GET':
        req_dict = request.GET

    if req_dict is None:
        return create_error_rsp("No supplied account", 401)
    elif not settings.require_authentication:
        return create_generic_rsp({'status': 'success'}, 200)
    else:
        username = req_dict.get('username', None)
        password = req_dict.get('password', None)
        user = authenticate(username=username, password=password)

        if user is not None:
            if user.is_active:
                login(request, user)
                return create_generic_rsp({'status': 'success'}, 200)
            else:
                return create_error_rsp("Account is disabled", 401)
        elif username is None or password is None:
            return create_error_rsp("Missing account parameters", 401)
        else:
            return create_error_rsp("No such account", 401)


@csrf_exempt
@transaction.atomic
def slingbox(request):
    """
    End point used by the Slingbox to either create a new key, or get an existing key.  It expects the request
    to be a POST, with a json body.  It must have either a 'key_id' field when an existing key is being requested,
    or a keyconfig field, if a new key should be generated.  It will return a json block with the results.
    """
    if request.method != "POST" or request.META.get("CONTENT_TYPE") != JSON_TYPE:
        return create_error_rsp("Invalid request.", 404)

    # FIXME, need to do authentication
    try:
        req_dict = json.loads(request.body.decode())
        config_name = req_dict.get("keyconfig", None)
        key_id = req_dict.get("key_id", None)

        if key_id:  # use the key indicated
            try:
                key_id = uuid.UUID(key_id)  # from string to UUID
            except Exception:
                return create_error_rsp("Malformed key_id, must be uuid", 400)
            use_key = Key.objects.get(pk=key_id)
        elif config_name:  # generate a new key
            # should this be updated to use the PreCachedKeys?
            key_conf = KeyConfig.objects.get(pk=config_name)
            use_key = Key.create(key_conf, "slingbox")
            use_key.save()
            key_id = use_key.id
        else:
            return create_error_rsp("Missing key_id and keyconfig", 400)

        result = {
            "key_id": key_id.hex,
            "key": use_key.secret.hex(),
            "expires": use_key.expires(),
            "providers": use_key.providers_meta
        }
        return create_generic_rsp(result, 200)
    except Key.DoesNotExist:
        return create_error_rsp("No Key found", 404)
    except KeyConfig.DoesNotExist:
        return create_error_rsp("No Key Config found", 404)
    except Exception as ex:
        return create_error_rsp("Unknown error encountered: %s" % ex, 500)


def regenerate_metadata_for_keyconfig(request, kc_name):
    from pysrc.keystore_app.tasks.local_tasks import reset_key_metadata
    reset_key_metadata.apply_async([kc_name], countdown=5)  # execute in 5 seconds
    return HttpResponseRedirect(request.META.get('HTTP_REFERER', '/'))
