# noinspection PyUnresolvedReferences
from django.urls import path, re_path

import pysrc.keystore_app.views as views

# any standard programming name, can't start with dash, underscore, or number
VALID_CHARS = '[A-Za-z0-9_-]'
CONFIG_PATTERN = VALID_CHARS + '+'
CONTENT_PATTERN = VALID_CHARS + '+'
GUID_CHAR = "[A-Fa-f0-9]"
GUID_PATTERN = "".join([GUID_CHAR, "{32}|(",
    GUID_CHAR, "{8}-", GUID_CHAR, "{4}-", G<PERSON>D_CHAR, "{4}-", GUID_CHAR, "{4}-", GUI<PERSON>_CHAR, "{12})"])

urlpatterns = [
    # handle both start and end segments
    re_path(r'^get/(?P<config>' + CONFIG_PATTERN + ')'
        r'/(?P<content>' + CONTENT_PATTERN + ')'
        r'/(?P<stream>' + GUID_PATTERN + ')'
        r'/(?P<start>\d+)(-(?P<end>\d+))?$', views.get_key, name='get_key'),

    # handle block endpoint
    re_path(r'^block/(?P<config>' + CONFIG_PATTERN + ')'
        r'/(?P<content>' + CONTENT_PATTERN + ')'
        r'/(?P<stream>' + GUID_PATTERN + ')'
        r'/(?P<block_num>\d+)$', views.get_block, name='get_block'),

    # handle bulk/generate endpoint
    re_path(r'^bulk/generate/?$', views.generate_bulk, name='generate_bulk'),

    # handle bulk/get endpoint
    re_path(r'^bulk/get/(?P<bulk_request_id>' + GUID_PATTERN + ')$', views.get_bulk, name='get_bulk'),

    # handle rsdvr
    re_path(r'^rsdvr/(?P<config>' + CONFIG_PATTERN + ')'
        r'/(?P<user_guid>' + GUID_PATTERN + ')$', views.get_rsdvr_keys, name='get_rsdvr_keys'),

    # handle the 'status' endpoint
    re_path(r'^_?status(?:\.json)?$', views.status, name='status'),

    # used to get a raw key, mainly for testing dynamux
    re_path(r'^raw/(?P<stream>' + GUID_PATTERN + r')/(?P<segment>\d+)$',
        views.raw_key_from_stream, name='raw_key_from_stream'),
    re_path(r'^raw/(?P<key_id>' + GUID_PATTERN + ')$', views.raw_key, name='raw_key'),

    # handle slingbox
    path('slingbox', views.slingbox, name="slingbox"),

    # used to remove a given key
    re_path(r'^remove/(?P<key_id>' + GUID_PATTERN + ')$', views.remove_key, name='remove_key'),

    # used to get full details on an existing key
    re_path(r'^info/(?P<key_id>' + GUID_PATTERN + ')$', views.key_info, name='key_info'),

    # used to get full details on an existing key
    re_path(r'^usage/(?P<key_id>' + GUID_PATTERN + ')$', views.key_usage, name='key_usage'),

    # handle malformed arguments
    re_path(r'^get/(?P<config>.*)/(?P<content>.*)/(?P<stream>.*)/(?P<start>.*)$',
        views.bad_url, name='bad_url'),

    # handle asking for keystore version
    path('version', views.version, name='version'),

    # handle asking for keystore health
    path('health', views.health, name='health'),

    # handle asking for keystore ready
    path('ready', views.ready, name='ready'),

    # handle logging in
    path('login', views.keystore_login, name='login'),

    # catch-all for bad requests, must  be last entry
    re_path(r'^.*$', views.get_empty, name='get_empty')
]
