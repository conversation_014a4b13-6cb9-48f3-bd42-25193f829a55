# pylint: disable=unnecessary-pass
from django.http import HttpResponse


class KeyConfigInUseException(Exception):
    """
    Class thrown by the admin interface when someone attempts to edit a KeyConfig that has
    keys associated with it.
    """
    pass


class ExceptionMiddleware(object):
    """
    Custom middleware class to intercept exceptions from the admin and views pages and do any special
    processing required.
    """

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        return self.get_response(request)

    def process_exception(self, _request, exception):
        """
        This method is used to look at any exceptions raised by the admin/views and generate any custom
        responses.  It currently looks for KeyConfigInUseExceptions, and generates a 409 response, with
        an appropriate message to the user.
        :param _request - The request that caused the exception, unused here.
        :param exception - The exception that was raised.
        :return A HttpResponse object, or None.
        """
        if isinstance(exception, KeyConfigInUseException):
            # don't bother with a json response, since this will only be seen by a human anyway
            response = HttpResponse(str(exception))
            response.status_code = 409  # 409 is "conflict"
            return response
        return None
