# pylint: disable=line-too-long
# noinspection PyUnresolvedReferences
from django.urls import re_path

import pysrc.keystore_app.views as views

app_name = "keystore_app"

urlpatterns = [
    re_path(r'^regenerate_metadata_for_keyconfig/(?P<kc_name>.*)/$',
        views.regenerate_metadata_for_keyconfig,
        name='regenerate_metadata_for_keyconfig'),
    re_path(r'^.*$', views.get_keyconfigs, name='get_keyconfigs'),
]
