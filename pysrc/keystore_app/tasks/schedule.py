# system packages
from datetime import timedelta

_TASK_PATH = "pysrc.keystore_app.tasks.local_tasks."

periodic_schedule = {
    "remove expired objects": {
        "task": _TASK_PATH + "remove_expired_objects",
        "schedule": timedelta(hours=4)
        },
    "pre generate keys": {
        "task": _TASK_PATH + "pre_generate_keys",
        "schedule": timedelta(minutes=5)
        },
    "push failed keys": {
        "task": _TASK_PATH + "push_failed_keys",
        "schedule": timedelta(minutes=3)
        },
    }
