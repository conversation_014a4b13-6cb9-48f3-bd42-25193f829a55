import datetime
from pysrc.keystore_srv.celerysetup import app
# to debug celery tasks use these line, and then telnet back into the remote box at the correct port
#from celery.contrib import rdb
#rdb.set_trace()

# logging in celery is slightly different, so this gets it's logger so everthing else can be unchanged
from celery.utils.log import get_task_logger
from django.utils import timezone
from django.db.models import DateTimeField, ExpressionWrapper, F

task_log = get_task_logger(__name__)

@app.task(ignore_result=False, track_started=True, bind=True)
def generate_bulk_keys(_self, key_config_name, num_keys):
    # keystore packages
    from pysrc.keystore_app.models import Key, KeyConfig

    task_log.info("Started processing bulk key request")
    #from celery.contrib import rdb; rdb.set_trace()
    keys = []
    try:
        for _ in range(num_keys):
            # generate a new key
            key_config = KeyConfig.objects.get(pk=key_config_name)
            use_key = Key.create(key_config, "bulk")
            use_key.save()
            keys.append(use_key.id)

        task_log.info("Finished processing bulk key request")
        return keys

    except Exception as ex:
        task_log.error("Problem in generate_bulk_keys %s", ex)
        raise ex

@app.task(ignore_result=True)
def remove_expired_objects():
    """
    This method deletes all the keys in the database that have expired (which deletes the associated KeyUsage as well).
    It then looks at the list of Streams, and any that no longer have any keys are also deleted.
    """
    # keystore packages
    from pysrc.keystore_app.models import Key, Stream

    now = timezone.now()
    two_weeks_ago = now - datetime.timedelta(weeks=2)
    keys = Key.objects.filter(
        accessed__lt = two_weeks_ago
    ).annotate(
        delta = ExpressionWrapper(
            F('config__lifetime') * datetime.timedelta(hours=1) + F('created'),
            output_field=DateTimeField()
        )
    ).filter(
        delta__lt = now
    )
    for key in keys.iterator():
        key.delete()

    # delete any streams that no longer have any keys
    streams_with_no_keys = Stream.objects.filter(keyusage__isnull=True)
    for strm in streams_with_no_keys:
        strm.delete()


@app.task(ignore_result=True)
def pre_generate_keys():
    """
    Method used to look for all the PrecCachedKeys for the various content-IDs and give them time to make more
    keys.  If they already have the maximum number cached, it turns into a no-op.
    """
    # keystore packages
    from pysrc.keystore_app.models import PreCachedKeys

    for cache in PreCachedKeys.objects.all():
        cache.load_cache()


@app.task(ignore_result=True)
def push_failed_keys():
    """
    Method used to look for keys that don't have all their metadata, and push missing data to the approrpriate
    DRM system.  It maintains a list of providers that are still failing so that if we have multiple keys that
    are missing metadata, and they use the same provider, we only try the provider one, instead of hammering
    for each key where it failed.
    """
    # keystore packages
    from pysrc.keystore_app.models import Key

    failed_providers = []
    for key in Key.objects.filter(has_all_meta=False).order_by('-created')[:1000]:
        missing_providers = key.missing_providers()
        if missing_providers and key.content_id:
            for x in missing_providers:
                if x not in failed_providers:
                    key.generate_missing_meta(key.content_id)  # does a save inside if it works, can update multiple
                    failed_providers.extend(key.missing_providers())
                    failed_providers = list(set(failed_providers))  # remove duplicates
                    break
        elif not missing_providers:
            key.has_all_meta = True
            key.save()


@app.task(ignore_result=True)
def reset_key_metadata(keyconfig_name):
    """
    Method used to take a keyconfig and invalidate the metadata for all the keys using that keyconfig.  It is
    done as a celery task because based on the number of keys, it can take a long time to execute (before it was
    in the KeyConfig post-save handler, which caused the web UI to stall while it was invalidating all the
    metadata.  It clears out all the provider metadata for the keys (except SecureMedia, since we can't push keys
    to SecureMedia), and then saves the changes to the key.  Thus all the keys get marked as missing metadata, so
    we call push_failed_keys.
    @param keyconfig_name - The name of keyconfig to use when looking for keys to reset the metadata of.
    """
    # keystore packages
    from pysrc.keystore_app.models import Key, KeyConfig

    try:
        key_config = KeyConfig.objects.get(name=keyconfig_name)
        for key in Key.objects.filter(config=key_config):
            key.has_all_meta = False
            for meta in key.providers_meta:
                if meta["type"] == "securemedia":
                    key.providers_meta = [meta]  # array of 1 item, a dictionary of secure media metadata
                    break
            else:
                # didn't break out of for-loop
                key.providers_meta = []
            key.save()
        push_failed_keys()
    except KeyConfig.DoesNotExist:
        task_log.error("Problem in reset_key_metadata, can't find keyconfig: %s", keyconfig_name)
