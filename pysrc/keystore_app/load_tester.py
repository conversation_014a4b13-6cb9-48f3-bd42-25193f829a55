#!/usr/bin/env python

from __future__ import print_function
from gevent import monkey
monkey.patch_all()

import argparse, uuid, random, grequests, logging, datetime, time
import json  # pylint: disable=unused-import
timeout = good = bad = 0


def _process_cli_args():
    parser = argparse.ArgumentParser(description="A program that attempts to stress-test the keystore")
    parser.add_argument("--host", help="The host to hit", default="localhost")
    parser.add_argument("--port", help="The port to use", type=int, default=8000)
    parser.add_argument("--threads", help="The number of threads to use for the requests", type=int, default=1)
    parser.add_argument("--requests", help="The number of requsts to make", type=int, default=1)
    parser.add_argument("--streams", help="The max number of unique streams to create", type=int, default=100)
    parser.add_argument("--keyconfig", help="The key config to use for testing", default="load_testing")
    parser.add_argument("--method", help="The key endpoint to use", default="get", choices=["get", "block"])
    return parser.parse_args()


def callback(rsp, *_args, **_kwargs):
    """
    This procedure is called when we get a response from a grequest.  It just looks at the response to keep
    track of good/bad responses.  For bad responses, it prints out the content/reason for the failure.
    @param rsp - The response object.
    """
    global good, bad, timeout
    if rsp is None:
        timeout += 1
    elif not rsp.ok:
        bad += 1
        print(rsp.content)
        rsp.close()
    else:
        good += 1
        rsp.close()


def main():
    #logging.getLogger("Greenlet").setLevel(logging.CRITICAL)
    logging.disable(logging.CRITICAL)
    args = _process_cli_args()

    # create a template for requests
    url_template = "http://%s:%i/key/%s/%s/testing/" % (args.host, args.port, args.method, args.keyconfig)
    url_template += "%s/%i?create_time=%i&duration=2048"

    # generate random stream guids
    guids = []
    for _ in range(args.streams):
        guids.append(uuid.uuid4().hex)

    # load all the requests urls into array
    urls = []
    try:
        start_time = datetime.datetime.now()
        for x in range(args.requests):
            seg_num = random.randint(1, 1700) if args.method == "get" else random.randint(1, 5)
            stream_guid = random.choice(guids)
            urls.append(url_template % (stream_guid, seg_num, int(time.time())))

            if len(urls) == 1000 or x == args.requests - 1:
                req_list = [grequests.get(url, timeout=2, callback=callback, stream=False) for url in urls]
                #print json.dumps(urls, indent=4, separators=[',', ": "])
                _rsp_list = grequests.map(req_list, size=args.threads)
                urls = []
    except KeyboardInterrupt:
        pass
    end_time = datetime.datetime.now()

    print("results: good =", good, "\tbad =", bad, "\ttimeouts =", (args.requests - good - bad))
    print("total time = ", (end_time - start_time).total_seconds(), "sec")


if __name__ == '__main__':
    main()
