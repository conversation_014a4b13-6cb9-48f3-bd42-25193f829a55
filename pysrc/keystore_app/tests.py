###########################
# pylint: skip-file
# KeystoreTestCase.auth_get causes pylint to crash on pylint version:
# pylint 2.3.1
# astroid 2.2.5
# Python 3.5.2
# [GCC 5.4.0 20160609]
###########################

# pylint: disable=E1103,E1120,E1123,W0703,line-too-long,multiple-imports
# django packages
from django.test import TransactionTestCase
from django.core.exceptions import ValidationError
from django.test.client import RequestFactory
from django.urls import resolve
from django.db import IntegrityError
import mock
from unittest.mock import Mock

# System packages
import uuid, json, math, datetime, os, sys, binascii
from urllib.parse import urlparse, parse_qs
from unittest import skip, skipIf

import jsonschema

# keystore_app packages
from pysrc.keystore_app.models import *  # pylint: disable=W0401,W0614
from . import settings
from pysrc.keystore_app.tasks.local_tasks import push_failed_keys, remove_expired_objects, reset_key_metadata

# used to cause all the celery tasks to run synchronous, so we don't need to run celeryd for the unit tests
from celery import current_app

current_app.conf.CELERY_ALWAYS_EAGER = True

# the json schema for the keystore
schema = {}
JSON = "application/json"
NAGRA_WORKING = False  # TODO: set to True if we ever get a working nagra server
PRIMETIME_MOCK_DATA = '{"licenseServerUrl": "https://bet-qa.ccp.xcal.tv", "messageId": "0852e5fe-19a9-4d86-abee-b99cdce01129", "metadata": "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"}'


def validate_response_json(response):
    """
    Method used to test the response json from the keystore to ensure it conforms to the json
    schema defined in a separate file.  It is setup so it only reads the file once then stores
    the resulting schema, so we don't open/close the file a ton of times.
    :param response - The json (as a string) to see if it matches the schema.
    :return The json response from the keystore.
    """
    global schema  # pylint: disable=W0603
    if not schema:  # only read file if no schema yet
        with open('pysrc/keystore_app/schemas/encryption.schema.json', 'r') as schemaFile:
            schema = json.load(schemaFile)
    data = json.loads(response.content.decode(response.charset))  # load response from keystore
    assert 'error' not in data, 'JSON response was --> {!r}'.format(data)
    jsonschema.validate(data, schema)  # will throw an exception if its not valid
    return data


def mock_primetime(*args, **kwargs): # pylint:disable=W0613
    class MockResponse(object):
        def __init__(self, content, status_code):
            self.status_code = status_code
            self.content = content
            self.ok = True

        def json(self):
            return json.loads(self.content)

    data = PRIMETIME_MOCK_DATA
    my_response = MockResponse(data.encode(), 200)
    return my_response


class KeystoreTestCase(TransactionTestCase):
    """
    Parent class for Keystore unit tests.  Used to have common setup methods, and a special replacement
    for the client 'get' call used in standard django unit tests.
    """

    def setUp(self):
        """
        Called before each unit test.  It ensures we have a user to use for authentication in the database,
        along with 2 default providers.
        """
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='test', password='unittest')
        self.ng = NagraProvider(name='NG')
        self.internal = InternalProvider(name="INT")
        self.widevine = WidevineProvider(name="WV", provider="unittest")
        self.pr = PlayReadyProvider(name="PR")
        self.fp = FairPlayProvider(name="FP")
        self.pt = PrimetimeProvider(name="PT")
        self.ng.save()
        self.internal.save()
        self.widevine.save()
        self.pr.save()
        self.fp.save()
        self.pt.save()
        SecureMediaProvider._band_names = []  # get a fresh list of band names after each test

    def tearDown(self):
        """
        Called after each unit test.  It ensures that if we have a file full of band names from Secure Media,
        we delete it so it doesn't interfere with the next test.
        """
        band_names = os.path.join(sys.prefix, "var", "run", "band_names.pickle")
        if os.path.exists(band_names):
            os.remove(band_names)

    def auth_get(self, url):
        """
        Custom replacement for client.get, because unit tests don't use django middle, which is needed for
        authentication.  So this method fakes out the authentication so the keystore server will believe the
        request is coming from an authenticated session.  Because of how it works, params in the url need to be
        ripped out and placed in a dictionary and attached to the request.  It then calls the function the url
        would get directed to using reverse URL lookup.
        :param url - The url to send to the keystore.
        :return The HTTPResponse object from the keystore.
        """
        factory = RequestFactory()
        url_parts = urlparse(url)
        request = factory.get(url_parts.path, parse_qs(url_parts.query))
        request.user = self.user
        func, args, kwargs = resolve(url_parts.path)  # figure out which function should be called
        return func(request, *args, **kwargs)

class KeyConfigTests(KeystoreTestCase):
    def test_valid_name(self):
        kc = KeyConfig(name='valid_config_name', usage=KeyConfig.LIVE_KEY)
        kc.full_clean()  # runs validation

    def test_invalid_name(self):
        kc = KeyConfig(name='invalid config name', usage=KeyConfig.LIVE_KEY)  # spaces aren't allowed
        self.assertRaises(ValidationError, kc.full_clean)

    def test_invalid_usage(self):
        kc = KeyConfig(name='config_name', usage="foo")
        self.assertRaises(ValidationError, kc.save)

    def test_invalid_seg_size(self):
        kc = KeyConfig(name='kc_name', seg_size=-10, usage=KeyConfig.LIVE_KEY)  # seg_size must be positive
        self.assertRaises(ValidationError, kc.full_clean)

    def test_invalid_seg_size_type(self):
        kc = KeyConfig(name='kc_name', seg_size='ten', usage=KeyConfig.LIVE_KEY)  # seg_size must be an int
        self.assertRaises(ValidationError, kc.full_clean)

    def test_key_config_connections_valid_no_timeout(self):
        """ This test is used to do a 'key config' provider check, with no timeout. """
        kc = KeyConfig(name='multi_prov_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.widevine)
        rel.save()
        rel = KeyConfigRelation(key_config=kc, content_object=self.ng)
        rel.save()

        response = self.client.get('/check/%s' % kc.name)  # no timeout
        code = response.status_code  # hack to ignore checking status code
        self.assertIn(code, [200, 412])
        self.assertContains(response, 'providers', count=1, status_code=code)
        self.assertContains(response, '"url"', count=2, status_code=code)  # double quotes needed
        self.assertContains(response, 'status', count=2, status_code=code)
        self.assertContains(response, 'healthy', count=2, status_code=code)

    def test_key_config_connections_valid_with_timeout(self):
        """
        This test is used to do a 'key config' provider check, with a supplied timeout.
        """
        kc = KeyConfig(name='multi_prov_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.widevine)
        rel.save()
        rel = KeyConfigRelation(key_config=kc, content_object=self.ng)
        rel.save()

        response = self.client.get('/check/%s/10' % kc.name)
        code = response.status_code  # hack to ignore checking status code
        self.assertIn(code, [200, 412])
        self.assertContains(response, 'providers', count=1, status_code=code)
        self.assertContains(response, '"url"', count=2, status_code=code)  # double quotes needed
        self.assertContains(response, 'status', count=2, status_code=code)
        self.assertContains(response, 'healthy', count=2, status_code=code)

    def test_key_config_connections_empty(self):
        """
        This test is used to do a 'key config' provider check, with no providers.
        """
        kc = KeyConfig(name='empty_prov_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        response = self.client.get('/check/%s' % kc.name)  # no timeout
        self.assertContains(response, 'providers')
        self.assertContains(response, 'message')  # why providers is empty

    def test_key_config_connections_failure(self):
        """ This test is used to do a 'key config' provider check, with no providers. """
        response = self.client.get('/check/foo_config')
        self.assertContains(response, 'error', status_code=404)

    @skipIf(not NAGRA_WORKING, "already know Nagra not responding")
    def test_key_config_partial_failure(self):
        """ This test is used to do a 'key config' provider check, with no providers. """
        # give nagra a bad url (port is wrong), so it will fail the check call
        ng = NagraProvider(name='NG', url='http://**************:8181/cks-ws-keyAndSignalization/key')
        ng.save()
        kc = KeyConfig(name='multi_prov_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.sm)
        rel.save()
        rel = KeyConfigRelation(key_config=kc, content_object=ng)
        rel.save()

        response = self.client.get('/check/%s' % kc.name)  # no timeout
        self.assertEqual(response.status_code, 412)

    @skipIf(not NAGRA_WORKING, "already know Nagra not responding")
    def test_key_config_unresponsive_providers(self):
        """
        This test is used to check that if a provider isn't responding, we get back a 202.  And that
        when requested again the provider is working, we get a 200 with all the proper meta-data.
        """
        # give nagra a bad url (port is wrong), so it will fail the check call
        ng = NagraProvider(name="NG2", url="http://**************:8181/cks-ws-keyAndSignalization/key")
        ng.save()
        kc = KeyConfig(name="unresponsive_provider", usage=KeyConfig.VOD_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.sm)
        rel.save()
        rel = KeyConfigRelation(key_config=kc, content_object=ng)
        rel.save()

        stream_id = uuid.uuid4()
        response = self.auth_get("/key/get/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
        self.assertEqual(response.status_code, 202)
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)  # only secure media

        # normally you can't change a KeyConfig that has keys, but we're doing it here to simiulate
        # the nagra provider starting to work again, and run the periodic task by hand
        rel.delete()
        rel = KeyConfigRelation(key_config=kc, content_object=self.ng)
        rel.save()
        push_failed_keys()

        # now get the same key again, and this time it should have all the meta-data (assuming Nagra is running)
        response = self.auth_get("/key/get/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
        rsp_json = validate_response_json(response)
        if response.status_code == 200:  # nagra is responding
            self.assertEqual(len(rsp_json["keys"][0]["providers"]), 2)
        else:  # the default nagra server isn't working right now
            print("Nagra not responding")
            self.assertEqual(response.status_code, 202)
            self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)

    def test_changing_vod_providers(self):
        with mock.patch('pysrc.keystore_app.models.keyconfig.keyconfig_cleanup_metadata', autospec=True,
                        side_effect=reset_key_metadata):
            kc = KeyConfig.objects.create(name="busted_sm", usage=KeyConfig.VOD_KEY, lifetime=1, band_id="devenc009")
            KeyConfigRelation.objects.create(key_config=kc, content_object=self.fp)
            int_rel = KeyConfigRelation.objects.create(key_config=kc, content_object=self.internal)
            wv_rel = KeyConfigRelation.objects.create(key_config=kc, content_object=self.widevine)

            stream_id = uuid.uuid4()
            rsp = self.auth_get("/key/block/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
            self.assertEqual(rsp.status_code, 200, 'Response was --> {!r}'.format(rsp.content))
            self.assertEqual(Key.objects.count(), 1)
            self.assertEqual(len(Key.objects.get().providers_meta), KeyConfigRelation.objects.count())

            # remove 2 of the providers from the keyconfig, and then 'save' the keyconfig so it sees the change
            # an generates new metadata.  In the unit tests it happens instantly, but in the real-world a celery
            # task is used to do it, which can take a bit of time depending on the number of keys associated with
            # this keyconfig
            int_rel.delete()
            wv_rel.delete()
            kc.save()

            # check to see how many providers have metadata on the key now
            self.assertEqual(len(Key.objects.get().providers_meta), 1)
            rsp = self.auth_get("/key/block/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
            self.assertEqual(rsp.status_code, 200, 'Response was --> {!r}'.format(rsp.content))
            self.assertEqual(Key.objects.count(), 1)
            rsp_json = validate_response_json(rsp)
            self.assertTrue(len(rsp_json["keys"][0]["providers"]), 1)


class ProviderTests(KeystoreTestCase):
    
    @skipIf(not NAGRA_WORKING, "already know Nagra not responding")
    def test_nagra_connection(self):
        """
        This test is used to check the connection to the Nagra server.  If anything
        goes wrong, it should only ever raise a NagraException, any other exception
        is a failure.
        """
        try:
            self.assertTrue(NagraProvider.check_connection(use_timeout=5))
        except NagraException:
            global NAGRA_WORKING
            NAGRA_WORKING = False
            print('Nagra server not responding')
        except Exception as ex:
            self.fail("Checking Nagra connection caused an unexpected exception: %s" % str(ex))

    @skipIf(not NAGRA_WORKING, "already know Nagra not responding")
    def test_nagra_connection_url(self):
        """
        This test is used to check the connection to the Nagra server using
        a url.  If anything goes wrong, it should have a non-200 status code,
        with FAILED in the response.
        """
        response = self.client.get('/check/nagra')  # no timeout
        if response.status_code == 200:
            self.assertContains(response, "OK")
        else:  # no response
            self.assertContains(response, "FAILED", status_code=response.status_code)

        response = self.client.get('/check/nagra/1')  # supplied timeout
        if response.status_code == 200:
            self.assertContains(response, "OK")
        else:
            self.assertContains(response, "FAILED", status_code=response.status_code)

    def test_playready_connection(self):
        """
        This test is used to check the connection to the PlayReady server.  If anything
        goes wrong, it should only ever raise a PlayReadyException, any other exception
        is a failure.
        """
        try:
            self.assertTrue(PlayReadyProvider.check_connection(use_timeout=5))
        except PlayReadyException:
            print("PlayReady server not responding")
        except Exception as ex:
            self.fail("Checking PlayReady connection caused an unexpected exception: %s" % ex)

    def test_playready_connection_url(self):
        """
        This test is used to check the connection to the PlayReady server using a url.  If anything goes wrong,
        it should have a non-200 status code, with FAILED in the response.
        """
        response = self.client.get("/check/playready")  # no timeout
        if response.status_code == 200:
            self.assertContains(response, "OK")
        else:  # no response
            self.assertContains(response, "FAILED", status_code=response.status_code)

        response = self.client.get("/check/playready/1")  # supplied timeout
        if response.status_code == 200:
            self.assertContains(response, "OK")
        else:
            self.assertContains(response, "FAILED", status_code=response.status_code)

    def test_widevine_pssh(self):
        # pylint: disable=no-member
        kc = KeyConfig(name="wv_provider", usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.widevine)
        rel.save()

        # save locals so we can compare it against the pssh
        content_id = "foobar"
        stream_id = uuid.uuid4()
        response = self.auth_get("/key/get/{}/{}/{}/1".format(kc.name, content_id, stream_id.hex))

        # make sure the response was good
        self.assertEqual(response.status_code, 200)
        rsp_json = validate_response_json(response)

        # make sure we only have a widevine provider
        self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)  # only 1 provider
        provider_json = rsp_json["keys"][0]["providers"][0]
        self.assertEqual(provider_json["type"], "widevine")

        # check the pssh values to make sure they match the database, and values passed into generate key
        self.assertTrue("pssh" in provider_json)
        decoded_pssh = WidevineProvider.decode_pssh(provider_json["pssh"])
        self.assertEqual(decoded_pssh.content_id.decode(), content_id)
        self.assertEqual(decoded_pssh.provider, self.widevine.provider)
        key = Key.objects.get()
        self.assertEqual(uuid.UUID(decoded_pssh.key_id[0].decode()).hex, key.id.hex)

    def test_playready_pssh(self):
        # pylint: disable=no-member
        kc = KeyConfig(name="pr_provider", usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.pr)
        rel.save()

        # save locals so we can compare it against the pssh
        content_id = "foobar"
        stream_id = uuid.uuid4()
        response = self.auth_get("/key/get/%s/%s/%s/1" % (kc.name, content_id, stream_id.hex))

        # make sure the response was good
        self.assertEqual(response.status_code, 200)
        rsp_json = validate_response_json(response)

        # make sure we only have a widevine provider
        self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)  # only 1 provider
        provider_json = rsp_json["keys"][0]["providers"][0]
        self.assertEqual(provider_json["type"], "playready")

        # check the pssh values to make sure they match the database, and values passed into generate key
        self.assertTrue("pssh" in provider_json)

    def test_fairplay_metadata(self):
        # pylint: disable=no-member
        kc = KeyConfig(name="fp_provider", usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.fp)
        rel.save()
        response = self.auth_get("/key/block/{}/{}/{}/1".format(kc.name, "foobar", uuid.uuid4().hex))

        # make sure the response was good
        self.assertEqual(response.status_code, 200)
        rsp_json = validate_response_json(response)

        # make sure we only have a fairplay provider
        self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)  # only 1 provider

        # check the response for proper values, most of the other checking is done by json validation
        # against the schema
        provider_json = rsp_json["keys"][0]["providers"][0]
        self.assertEqual(provider_json["type"], "fairplay")

    @mock.patch("pysrc.keystore_app.models.primetime.requests.post", side_effect=mock_primetime)
    def test_primetime_metadata(self, http_function): # pylint:disable=W0613
        # pylint: disable=no-member
        kc = KeyConfig(name="pt_provider", usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.pt)
        rel.save()
        response = self.auth_get("/key/block/%s/%s/%s/1" % (kc.name, "foobar", uuid.uuid4().hex))

        # make sure the response was good
        self.assertEqual(response.status_code, 200)
        rsp_json = validate_response_json(response)

        # make sure we only have a peimetime provider
        self.assertEqual(len(rsp_json["keys"][0]["providers"]), 1)  # only 1 provider

        # check the response for proper values, most of the other checking is done by json validation
        # against the schema
        provider_json = rsp_json["keys"][0]["providers"][0]
        self.assertEqual(provider_json["type"], "primetime")


class ObjectDeletionTests(KeystoreTestCase):
    def test_stream_deletion(self):
        """
        Test to ensure that when a key has expired, using a task to delete the Key also takes out the key usage,
        and any stream that is/was using the expired key.
        """
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        # create 2 separate streams and keys
        self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', uuid.uuid4().hex))
        self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', uuid.uuid4().hex))

        self.assertEqual(Key.objects.count(), 2)
        self.assertEqual(KeyUsage.objects.count(), 2)
        self.assertEqual(Stream.objects.count(), 2)
        # grab one key and adjust it so that is will be expired
        key = Key.objects.all()[0]
        key.created = key.created - datetime.timedelta(weeks=3)
        key.accessed = key.accessed - datetime.timedelta(weeks=3) + datetime.timedelta(hours=1)
        key.save()
        remove_expired_objects()  # should take out 1 Key, KeyUsage, and Stream
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)
        self.assertEqual(Stream.objects.count(), 1)


class KeyUsageSegmentRotationTests(KeystoreTestCase):
    @skip("Only run by hand, needs breakpoint inside the 'get_key' view function")
    def test_duplicate_key_creation(self):
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.sm)
        rel.save()

        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', stream_id.hex))
        self.assertContains(response, "integrity", status_code=500)

    def test_block(self):
        """
        This test is used to verify we can generate a single key from a KeyConfig using a block request.
        It does one block, then another (same stream), and makes sure they are different keys.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='block_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.SEGMENT_ROT)
        kc.save()

        # first block, so first key
        response = self.auth_get("/key/block/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))

        # request higher block, causing a new key for same stream
        response = self.auth_get("/key/block/%s/%s/%s/3" % (kc.name, "foo_id", stream_id.hex))
        rsp_json2 = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertNotEqual(rsp_json['keys'][0]['key_id'], rsp_json2['keys'][0]['key_id'])  # make sure keys are diff

    def test_rsdvr_endpoint(self):
        """
        This test is used to verify that when using the rsdv endpoint, we only ever get a single key (even though
        it is an array of keys).  It also check to make sure a new key is created when the previous key expired and
        a new request comes in.
        """
        user_guid = uuid.uuid4()
        kc = KeyConfig(name='rsdvr_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.SEGMENT_ROT,
                lifetime=1)
        kc.save()

        # first call, so first key
        rsp = self.auth_get("/key/rsdvr/%s/%s" % (kc.name, user_guid.hex))
        rsp_json = validate_response_json(rsp)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(user_guid))
        self.assertEqual(rsp_json["encryption_block_size"], 85000)

        # adjust creation time of the key to a while ago, to pretend it has expired, and is acutally out of window
        key = Key.objects.get()
        key.created = key.created - datetime.timedelta(weeks=2, hours=kc.lifetime)
        key.accessed = key.created
        key.save()

        # second call, so should be different key, but still only 1 key
        rsp = self.auth_get("/key/rsdvr/%s/%s" % (kc.name, user_guid.hex))
        rsp_json = validate_response_json(rsp)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertNotEqual(uuid.UUID(rsp_json["keys"][0]["key_id"]), key.id)

        # the old/first key should be deleted, should only have 1 of a much of records
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)
        self.assertEqual(Stream.objects.count(), 1)
        self.assertEqual(Stream.objects.get().id, user_guid)

    def test_duplicate_key_usage(self):
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', stream_id.hex))
        validate_response_json(response)
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)

        usage = KeyUsage.objects.first()
        dupe_usage = KeyUsage(key=usage.key, stream=usage.stream, startSeg=usage.startSeg, endSeg=100)
        self.assertRaises(IntegrityError, dupe_usage.save)  # can't do any more db work because this transaction fails

    def test_boundary_requests(self):
        """
        This test is to ensure when we cross the segment block size boundary we get different keys.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        all_responses = []
        stream_id = uuid.uuid4()
        for seg_num in range(99, 102):
            response = self.auth_get('/key/get/%s/%s/%s/%i' % (kc.name, 'one_segment', stream_id.hex, seg_num))
            rsp_json = validate_response_json(response)
            all_responses.append(rsp_json)
        self.assertEqual(all_responses[0], all_responses[1])  # keys for seg 99 and 100 should be the same
        self.assertNotEqual(all_responses[1], all_responses[2])  # keys for 100 and 101 should be different
        self.assertEqual(Key.objects.count(), 2)
        self.assertEqual(KeyUsage.objects.count(), 2)

    def test_internal_one_segment(self):
        """
        This test is to ensure when 1 segment is requested, the response has the proper number of entries.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', usage=KeyConfig.VOD_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        stream_id = uuid.uuid4()
        rsp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', stream_id.hex))
        if rsp.status_code == 200:
            rsp_json = validate_response_json(rsp)
            self.assertEqual(len(rsp_json['keys']), 1)
            self.assertEqual(len(rsp_json['keys'][0]['providers']), 1)
            self.assertEqual(rsp_json["keys"][0]["providers"][0]["type"], self.internal.get_type())
            self.assertEqual(rsp_json['encryption_block_size'], kc.seg_size)
        else:
            # test failed, but check to make sure we get a proper error message
            rsp_json = json.loads(rsp.content.decode())
            self.assertIn("error", rsp_json)
            self.fail("Failed to get a key from Secure Media")

    @skipIf(not NAGRA_WORKING, "already know Nagra not responding")
    def test_ng_one_segment(self):
        """
        This test is to ensure when 1 segment is requested, the response has the proper
        number of entries.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.ng)
        rel.save()

        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', stream_id.hex))
        rsp_json = validate_response_json(response)
        num_prov = len(rsp_json['keys'][0]['providers'])
        self.assertEqual(len(rsp_json['keys']), 1)
        self.assertEqual(rsp_json['encryption_block_size'], kc.seg_size)
        if num_prov == 1:
            self.assertContains(response, stream_id, count=1)
            self.assertContains(response, 'drm_sys_id', count=1)
            self.assertContains(response, 'drm_name', count=1)
            self.assertContains(response, 'key_uri', count=1)
        else:
            print("Nagra not responding")

    def test_no_provider_one_segment(self):
        """
        This test is to ensure when 1 segment is requested with no provider, the response has the proper
        number of entries.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'foo_id', stream_id.hex))
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json['keys']), 1)
        self.assertEqual(len(rsp_json['keys'][0]['providers']), 0)
        self.assertContains(response, stream_id, count=1)
        self.assertContains(response, "key_id", count=1)
        self.assertEqual(rsp_json['encryption_block_size'], kc.seg_size)

    def test_changing_segment_size(self):
        """
        This test is to ensure that we handle changing the segment size on the fly.  New keys should have
        the proper size.
        """
        with mock.patch('pysrc.keystore_app.models.keyconfig.keyconfig_cleanup_metadata', autospec=True,
                        side_effect=reset_key_metadata):
            orig_seg_size = 50
            kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009',
                           seg_size=orig_seg_size)
            kc.save()

            rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
            rel.save()

            stream_id = uuid.uuid4()
            # special bandID for dev work
            response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'garbage', stream_id.hex))
            validate_response_json(response)
            json_rsp = json.loads(response.content.decode())
            self.assertEqual(json_rsp['keys'][0]['end_seg'], kc.seg_size)
            self.assertEqual(json_rsp['encryption_block_size'], kc.seg_size)

            # pretend the user changed the segment size
            kc.seg_size = 300
            kc.save()

            stream_id2 = uuid.uuid4()  # require a different stream to use new seg_size
            response = self.auth_get('/key/get/%s/%s/%s/1-1000' % (kc.name, 'garbage', stream_id2.hex))
            validate_response_json(response)
            json_rsp = json.loads(response.content.decode())
            self.assertEqual(Key.objects.count(), 5)
            self.assertEqual(json_rsp['keys'][0]['end_seg'] - json_rsp['keys'][0]['start_seg'] + 1, kc.seg_size)
            self.assertEqual(json_rsp['encryption_block_size'], kc.seg_size)

            # verify that the old stream sticks with own value, not the changed/new keyconfig value
            response = self.auth_get('/key/get/%s/%s/%s/100' % (kc.name, 'garbage', stream_id.hex))
            validate_response_json(response)
            json_rsp = json.loads(response.content.decode())
            self.assertEqual(json_rsp['encryption_block_size'], orig_seg_size)

    def test_multiple_providers_one_segment(self):
        """
        This test is to ensure when 1 segment is requested what has multiple providers,
        the response has the proper number of entries.  It also ensure that PlayReady
        is the first provider in the list of providers.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', usage=KeyConfig.VOD_KEY)
        kc.save()

        KeyConfigRelation.objects.create(key_config=kc, content_object=self.fp)
        KeyConfigRelation.objects.create(key_config=kc, content_object=self.internal)
        KeyConfigRelation.objects.create(key_config=kc, content_object=self.pr)
        KeyConfigRelation.objects.create(key_config=kc, content_object=self.widevine)

        stream_id = uuid.uuid4()
        rsp = self.auth_get('/key/get/{}/{}/{}/10'.format(kc.name, 'multi_prov_1_seg', stream_id.hex))
        if rsp.status_code == 200 or rsp.status_code == 202:
            rsp_json = validate_response_json(rsp)
            k = Key.objects.get()
            self.assertTrue(k.has_all_meta)
            self.assertEqual(len(rsp_json["keys"][0]["providers"]), KeyConfigRelation.objects.count())
            self.assertEqual(rsp_json["stream_id"], str(stream_id))
            self.assertEqual(rsp_json["encryption_block_size"], kc.seg_size)
            self.assertEqual(rsp_json["keys"][0]["providers"][0]["type"], "playready")  # playready first
        else:
            # test failed, but check to make sure we get a proper error message
            rsp_json = json.loads(rsp.content.decode())
            self.assertIn("error", rsp_json)
            self.fail("Failed to get any keys when trying multiple providers")

    def test_multiple_vod_segments(self):
        """
        This test is to ensure when asking for multiple segments for non-live content, we get back a single
        key.  It also checks that only single entries are put into the database.
        """
        # prepare test data
        kc = KeyConfig(name='vod_config', usage=KeyConfig.VOD_KEY)
        kc.save()

        num_segments = 3
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10-%s' % (kc.name, 'multi_vod_seg', stream_id.hex,
                                                                (10 + kc.seg_size * (num_segments - 1))))
        # since it is VoD, it is always just 1 segment, never multiple
        validate_response_json(response)
        self.assertEqual(KeyUsage.objects.count(), 1)
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(Stream.objects.count(), 1)
        self.assertContains(response, "key_id", count=1)

    def test_small_range_spanning_keys(self):
        """
        This test is to ensure when asking for a range that spans 2 keys, but the range in small, we still get the
        right number of keys.  This was exposed as a bug in production, so it is a fairly specific test.
        """
        # prepare test data
        kc = KeyConfig(name="live_config", usage=KeyConfig.LIVE_KEY)
        kc.save()
        start_range = 3*kc.seg_size - 5
        end_range = 3*kc.seg_size + 5

        stream_id = uuid.uuid4()
        response = self.auth_get("/key/get/%s/%s/%s/%i-%i" % (kc.name, "small_live_seg", stream_id.hex,
            start_range, end_range))
        validate_response_json(response)
        #print("/key/get/%s/%s/%s/%i-%i" % (kc.name, "small_live_seg", stream_id.hex, start_range, end_range))
        #print(json.dumps(json.loads(response.content), indent=4, separators=[',', ": "]))
        self.assertEqual(KeyUsage.objects.count(), 2)
        self.assertEqual(Key.objects.count(), 2)
        self.assertEqual(Stream.objects.count(), 1)
        self.assertContains(response, "key_id", count=2)

    def test_multiple_live_segments(self):
        """
        This test is to ensure when asking for multiple segments for live content, we get back the
        appropriate number of keys.  It also checks that the right number of items are put into the
        database.
        """
        # prepare test data
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        num_segments = 3
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10-%s' % (kc.name, 'multi_live_seg', stream_id.hex,
                                                                (10 + kc.seg_size * (num_segments - 1))))
        validate_response_json(response)
        self.assertEqual(KeyUsage.objects.count(), num_segments)
        self.assertEqual(Key.objects.count(), num_segments)
        self.assertEqual(Stream.objects.count(), 1)
        self.assertContains(response, "key_id", count=num_segments)

    def test_large_range_request(self):
        """
        This test is used to ensure a large number of keys from a single range request is handled correctly.  It
        should only generate some of the keys, not all of them.
        """
        # prepare test data
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        num_segments = 2000
        stream_id = uuid.uuid4()
        rsp = self.auth_get('/key/get/%s/%s/%s/10-%s' % (kc.name, 'multi_live_seg', stream_id.hex,
                                                    (10 + kc.seg_size * (num_segments - 1))))
        self.assertEqual(rsp.status_code, 202)
        self.assertEqual(KeyUsage.objects.count(), settings.max_range_generation)
        self.assertEqual(Key.objects.count(), settings.max_range_generation)
        self.assertEqual(Stream.objects.count(), 1)

    def test_fast_requests(self):
        """
        Test used to verify a bunch of fast requests don't end up interfering with each other.  It fires off a
        series of requests quickly before making sure the right number of keys/streams were created.
        """
        num_segments = 20
        configs = []

        # prepare configs
        for x in range(num_segments):
            configs.append(KeyConfig(name='taped_config' + str(x), usage=KeyConfig.LIVE_KEY, band_id="devenc009",
                lifetime=1))
            configs[x].save()
            rel = KeyConfigRelation(key_config=configs[x], content_object=self.internal)
            rel.save()

        # do requests
        for x in range(num_segments):
            stream_id = uuid.uuid4()
            rsp = self.auth_get('/key/get/%s/%s/%s/10' % (configs[x].name, 'fast_req' + str(x), stream_id.hex))
            self.assertEqual(rsp.status_code, 200, 'For request #{}, response was --> {!r}'.format(x, rsp.content))

        self.assertEqual(KeyUsage.objects.count(), num_segments)
        self.assertEqual(Key.objects.count(), num_segments)
        self.assertEqual(Stream.objects.count(), num_segments)

    def test_multiple_key_requests(self):
        """
        This test is to ensure when multiple requests come in the from the same segment
        of the same stream, they all get the same results, and that extra entries aren't
        put into the database.
        """
        # prepare test data
        kc = KeyConfig(name='taped_config', usage=KeyConfig.VOD_KEY)
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        stream_id = uuid.uuid4()
        response1 = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'multi_key_req', stream_id.hex))
        if response1.status_code != 200:
            # test failed, but check to make sure we get a proper error message
            rsp_json = json.loads(response1.content.decode())
            self.assertIn("error", rsp_json)
            self.fail("Failed to get a key from Secure Media")
        rsp_json = validate_response_json(response1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json['keys'][0]['providers']), 1)
        self.assertEqual(rsp_json["keys"][0]["providers"][0]["type"], self.internal.get_type())

        # do a few more requests
        resp = []
        num_requests = 10
        for _ in range(num_requests):
            resp.append(self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'multi_key_req', stream_id.hex)))

        # make sure still only a single usage
        self.assertEqual(KeyUsage.objects.count(), 1)
        self.assertEqual(Key.objects.count(), 1)

        # make sure everyone got back the same results
        json1 = json.loads(response1.content.decode())
        for r in resp:
            j = json.loads(r.content.decode())
            self.assertEqual(json1, j)

    def test_disjoint_key_requests(self):
        """
        This test is to ensure we still generate segments correctly when disjoint segments are requested.
        """
        # prepare test data
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'disjoint_key_req', stream_id.hex))
        rsp_json = validate_response_json(resp)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(Key.objects.count(), 1)

        resp = self.auth_get('/key/get/%s/%s/%s/%i' % (kc.name, 'disjoint_key_req', stream_id.hex, settings.segment_size + 10))
        rsp_json = validate_response_json(resp)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(Key.objects.count(), 2)  # make sure we didn't fill in all the earlier keys

    def test_disjoint_range_requests(self):
        """
        This test is to ensure when you do disjoint range requests, you still get all the keys needed.  The important
        part of this test is to do the higher range request first (a bug was seen for this case).
        """
        # prepare test data
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY, seg_size=50)
        kc.save()

        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/1500-1510' % (kc.name, 'disjoint_range_req', stream_id.hex))
        rsp_json = validate_response_json(resp)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(Key.objects.count(), 1)

        resp = self.auth_get('/key/get/%s/%s/%s/1-100' % (kc.name, 'disjoint_range_req', stream_id.hex))
        rsp_json = validate_response_json(resp)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json["keys"]), 2)
        self.assertEqual(Key.objects.count(), 3)  # make sure we didn't fill in all the earlier keys

    def test_overlap_key_requests(self):
        """
        This test is to ensure we still generate segments correctly when requesting a range that covers
        an existing key usage.
        """
        # prepare test data
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY, lifetime=24)
        kc.save()

        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/%i' % (kc.name, 'overlap_req', stream_id.hex, settings.segment_size * 2 + 5))
        rsp_json1 = validate_response_json(resp)
        self.assertEqual(rsp_json1["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json1["keys"]), 1)
        self.assertEqual(Key.objects.count(), 1)

        resp = self.auth_get('/key/get/%s/%s/%s/10-%i' % (kc.name, 'overlap_req', stream_id.hex, settings.segment_size * 10))
        rsp_json2 = validate_response_json(resp)

        while resp.status_code == 202:  # make sure all the keys get returned
            resp = self.auth_get('/key/get/%s/%s/%s/10-%i' % (kc.name, 'overlap_req', stream_id.hex, settings.segment_size * 10))
            rsp_json2 = validate_response_json(resp)

        self.assertEqual(rsp_json2["stream_id"], str(stream_id))
        self.assertEqual(len(rsp_json2["keys"]), 10)
        self.assertEqual(Key.objects.count(), 10)
        self.assertEqual(KeyUsage.objects.count(), 10)
        self.assertEqual(rsp_json1['keys'][0], rsp_json2['keys'][2])  # same key

    def test_key_removal(self):
        """
        Test to ensure when removing a key, it actually disappears.  It's corresponding KeyUsage
        should also be removed; however, the KeyConfig should not be removed.
        """
        # prepare test data
        self.test_internal_one_segment()
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)
        self.assertEqual(KeyConfig.objects.count(), 1)
        key = Key.objects.get()

        response = self.auth_get('/key/remove/%s' % key.id.hex)  # will create a key usage
        self.assertContains(response, "success")
        self.assertEqual(Key.objects.count(), 0)
        self.assertEqual(KeyUsage.objects.count(), 0)
        self.assertEqual(KeyConfig.objects.count(), 1)  # KeyConfig is still there
        response = self.auth_get('/key/remove/0123456789abcdef0123456789abcdef')
        self.assertContains(response, "No such key", status_code=404)

    def test_invalid_urls(self):
        """ This test is to ensure the proper responses are returned when invalid urls are used. """
        stream_id = uuid.uuid4()
        content_id = 'test_content'
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()
        response = self.auth_get('/key/get/%s/%s/%s/10' % ('no_such_config', content_id, stream_id.hex))
        self.assertContains(response, "No Key Config found", status_code=404)

        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'invalid content', stream_id.hex))
        self.assertContains(response, "Invalid content ID format", status_code=400)

        response = self.auth_get('/key/get/%s/%s/%s/seg' % (kc.name, 'test$content', stream_id.hex))  # no $ allowed
        self.assertContains(response, "Invalid content ID format", status_code=400)

        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, content_id, '123456789'))
        self.assertContains(response, "Invalid stream ID format", status_code=400)

        response = self.auth_get('/key/get/%s/%s/%s/seg' % (kc.name, content_id, stream_id.hex))
        self.assertContains(response, "Invalid start segment format", status_code=400)

        response = self.auth_get('/key/get/%s/%s/%s/0' % (kc.name, content_id, stream_id.hex))  # segments start at 1
        self.assertContains(response, "Invalid start segment value", status_code=400)

    def test_content_id_urls(self):
        """ This test is to ensure the proper responses are returned when various content urls are used. """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        # the format the encoders uses for vod
        resp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, stream_id.hex, stream_id.hex))
        self.assertContains(resp, stream_id, count=1)
        resp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, '1234', stream_id.hex))  # start with numbers
        self.assertContains(resp, stream_id, count=1)
        resp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, '_1234', stream_id.hex))  # start with underscore
        self.assertContains(resp, stream_id, count=1)
        resp = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, '-1234', stream_id.hex))  # start with dash
        self.assertContains(resp, stream_id, count=1)

    def test_end_before_start_seg(self):
        """
        This test is to ensure we get an error back if you have a 'live' keyconfig, and you request
        multiple segments, but have the 'start' segment greater than the 'end' segment.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='live_config', usage=KeyConfig.LIVE_KEY)
        kc.save()
        response = self.auth_get('/key/get/%s/%s/%s/20-10' % (kc.name, 'test_content', stream_id.hex))
        self.assertContains(response, "End segment before start segment", status_code=400)


class KeyUsageTimeRotationTests(KeystoreTestCase):
    def test_simple(self):
        """
        This test is used to verify we can generate a single key from a KeyConfig that is set to use time
        rotation.  It does one key, then another (same stream), and makes sure they are different keys.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()
        duration = 2048
        segments_per_key = 24 * 60 * 60 / (duration / 1000.0)

        # first segment, so first key
        response = self.auth_get('/key/get/%s/%s/%s/10?create_time=100&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(rsp_json['rotation_time'], str(kc.rotation_time))

        # request higher segment, causing a new key for same stream
        response = self.auth_get('/key/get/%s/%s/%s/%i?create_time=100&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, segments_per_key, duration))
        rsp_json2 = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(rsp_json['rotation_time'], str(kc.rotation_time))
        self.assertNotEqual(rsp_json['keys'][0]['key_id'], rsp_json2['keys'][0]['key_id'])  # make sure keys are diff

    def test_ranged(self):
        """
        This test is used to verify we can generate multiple keys from a KeyConfig that is set to use time
        rotation by specifying a segment range.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()
        duration = 2048
        segments_per_key = 24 * 60 * 60 / (duration / 1000.0)

        response = self.auth_get('/key/get/%s/%s/%s/1-%i?create_time=100&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, segments_per_key, duration))
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json['keys']), 2)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(rsp_json['rotation_time'], str(kc.rotation_time))
        end0 = rsp_json['keys'][0]['end_seg']
        start1 = rsp_json['keys'][1]['start_seg']
        self.assertEqual(end0 + 1, start1)  # make sure it starts on the next segment

    def test_block(self):
        """
        This test is used to verify we can generate a single key from a KeyConfig that is set to use time
        rotation.  It does one key, then another (same stream), and makes sure they are different keys.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()
        duration = 2048

        # first segment, so first key
        response = self.auth_get('/key/block/%s/%s/%s/1?create_time=100&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        rsp_json = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(rsp_json["rotation_time"], str(kc.rotation_time))

        # request higher block, causing a new key for same stream
        response = self.auth_get('/key/block/%s/%s/%s/3?create_time=100&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        rsp_json2 = validate_response_json(response)
        self.assertEqual(len(rsp_json["keys"]), 1)
        self.assertEqual(rsp_json["stream_id"], str(stream_id))
        self.assertEqual(rsp_json["rotation_time"], str(kc.rotation_time))
        self.assertNotEqual(rsp_json["keys"][0]["key_id"], rsp_json2["keys"][0]["key_id"])  # make sure keys are diff

    def test_bad_query_args(self):
        """
        This test is used to verify we can generate multiple keys from a KeyConfig that is set to use time
        rotation by specifying a segment range.
        """
        stream_id = uuid.uuid4()
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()
        duration = 2048

        # missing create_time
        response = self.auth_get('/key/get/%s/%s/%s/1?duration=%i' % (kc.name, 'foo_id', stream_id.hex, duration))
        self.assertContains(response, 'error', status_code=400)

        # missing duration
        response = self.auth_get('/key/get/%s/%s/%s/1?create_time=100' % (kc.name, 'foo_id', stream_id.hex))
        self.assertContains(response, 'error', status_code=400)

        # non-int create_time
        response = self.auth_get('/key/get/%s/%s/%s/1?create_time=hi?duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        self.assertContains(response, 'error', status_code=400)

        # non-int duration
        response = self.auth_get('/key/get/%s/%s/%s/1?create_time=100?duration=hi' % (kc.name, 'foo_id',
            stream_id.hex))
        self.assertContains(response, 'error', status_code=400)

        # negative create_time
        response = self.auth_get('/key/get/%s/%s/%s/1?create_time=-2?duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        self.assertContains(response, 'error', status_code=400)

        # negative duration
        response = self.auth_get('/key/get/%s/%s/%s/1?create_time=100?duration=-2' % (kc.name, 'foo_id',
            stream_id.hex))
        self.assertContains(response, 'error', status_code=400)

    def test_disjoint_key_requests(self):
        """
        This test is to ensure we still generate segments correctly when disjoint segments are requested.
        """
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()

        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=100&duration=2048' % (kc.name, 'foo_id', stream_id.hex))
        validate_response_json(resp)
        self.assertContains(resp, stream_id, count=1)
        self.assertContains(resp, "key_id", count=1)
        self.assertEqual(Key.objects.count(), 1)

        resp = self.auth_get('/key/get/%s/%s/%s/1000000?create_time=100&duration=2048' % (kc.name, 'foo_id',
            stream_id.hex))
        validate_response_json(resp)
        self.assertContains(resp, stream_id, count=1)
        self.assertContains(resp, "key_id", count=1)
        self.assertEqual(Key.objects.count(), 2)  # make sure we didn't fill in all the earlier keys

    def test_large_duration(self):
        """
        This test is to ensure we still generate segments correctly when the duration is larger than the standard
        2048 milliseconds.  It has the stream created at the rotation, so we get 1 full rotation, and can then
        look at how many segments the key is for.
        """
        # pylint: disable=no-member
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT)
        kc.save()
        duration = 10000  # 10 seconds
        segments_per_key = math.floor(24 * 60 * 60 / (duration / 1000.0))
        # set the creation time of the stream to the same as the rotation time
        create_time = kc.rotation_time.hour * 3600 + kc.rotation_time.minute * 60 + kc.rotation_time.second

        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=%i&duration=%i' % (kc.name, 'foo_id', stream_id.hex,
            create_time, duration))
        rsp_json = validate_response_json(resp)
        self.assertContains(resp, stream_id, count=1)
        self.assertContains(resp, "key_id", count=1)
        self.assertEqual(Key.objects.count(), 1)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_per_key, end_seg)

    def test_create_time(self):
        """
        This test is to ensure we still generate keys with the proper number of segments covered, with different
        segment create times (3 hours before, same time, 3 hours after).
        """
        # pylint: disable=no-member
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT,
                rotation_time=datetime.time(hour=3))  # rotate at 3am
        kc.save()
        duration = 2048
        segments_per_day = math.floor(24 * 60 * 60 / (duration / 1000.0))
        segments_3_hours = math.floor(segments_per_day / 8)

        # create time before rotation time
        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=0&duration=%i' % (kc.name, 'foo_id', stream_id.hex,
            duration))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_3_hours, end_seg)

        # create time AT rotation time
        stream_id = uuid.uuid4()
        create_time = kc.rotation_time.hour * 3600 + kc.rotation_time.minute * 60 + kc.rotation_time.second
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=%i&duration=%i' % (kc.name, 'foo_id', stream_id.hex,
            create_time, duration))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_per_day, end_seg)

        # create time after rotation time
        stream_id = uuid.uuid4()
        create_time += (3600 * 3)
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=%i&duration=%i' % (kc.name, 'foo_id', stream_id.hex,
            create_time, duration))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_per_day - segments_3_hours, end_seg)

    def test_changing_query_args(self):
        """
        This test is used to ensure that if the query args change from their original values, the Keystore will use
        the values supplied the first time.
        """
        # pylint: disable=no-member
        kc = KeyConfig(name='timed_config', usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.TIME_ROT,
                rotation_time=datetime.time(hour=3))  # rotate at 3am
        kc.save()
        duration = 2048
        segments_per_day = math.floor(24 * 60 * 60 / (duration / 1000.0))
        segments_3_hours = math.floor(segments_per_day / 8)

        # initial create
        stream_id = uuid.uuid4()
        resp = self.auth_get('/key/get/%s/%s/%s/1?create_time=0&duration=%i' % (kc.name, 'foo_id', stream_id.hex,
            duration))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_3_hours, end_seg)

        # same stream, but larger duration
        resp = self.auth_get('/key/get/%s/%s/%s/6000?create_time=0&duration=5000' % (kc.name, 'foo_id',
            stream_id.hex))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(segments_per_day + segments_3_hours, end_seg)

        # create time after rotation time
        resp = self.auth_get('/key/get/%s/%s/%s/50000?create_time=5000&duration=%i' % (kc.name, 'foo_id',
            stream_id.hex, duration))
        rsp_json = validate_response_json(resp)
        end_seg = rsp_json['keys'][0]['end_seg']
        self.assertEqual(2 * segments_per_day + segments_3_hours, end_seg)


class DynamuxTests(KeystoreTestCase):
    """
    These are tests for functions that are used by external systems that contact the
    Keystore (mainly dynamux stuff).
    """

    def test_raw_key_valid(self):
        """
        This test is to ensure when a raw key value is asked for using the key ID,
        the correct value is return as a string.
        """
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        # generate a key
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'raw_key', stream_id.hex))
        self.assertContains(response, stream_id, count=1)
        self.assertEqual(Key.objects.count(), 1)

        key = Key.objects.get()
        response = self.auth_get('/key/raw/%s' % key.id.hex)
        self.assertEqual(response.content, bytes(key.secret))

    def test_key_info(self):
        """
        This test ensures that all the fields are present when using the /key/info endpoint.
        """
        self.test_raw_key_valid()
        key = Key.objects.get()
        rsp = self.auth_get("/key/info/%s" % key.id)
        rsp_json = json.loads(rsp.content.decode())
        self.assertIn("has_all_meta", rsp_json)
        self.assertEqual(rsp_json["has_all_meta"], key.has_all_meta)
        self.assertEqual(rsp_json["key"], key.secret.hex())

    def test_raw_key_invalid(self):
        """
        This test is to ensure when a raw key value is asked for using the key ID,
        the correct error message is returned when it doesn't exist.
        """
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        # generate a key
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'raw_key', stream_id.hex))
        self.assertContains(response, stream_id, count=1)
        self.assertEqual(Key.objects.count(), 1)

        response = self.auth_get('/key/raw/0123456789abcdef0123456789abcdef')
        self.assertContains(response, "No Key found", status_code=404)

    def test_raw_key_from_stream_valid(self):
        """
        This test is to ensure when a raw key value is asked for using a stream ID and
        segement number, the correct value is returned as a string.
        """
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        # generate a key
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'raw_key', stream_id.hex))
        self.assertContains(response, stream_id, count=1)
        self.assertEqual(Key.objects.count(), 1)

        key = Key.objects.get()
        response = self.auth_get('/key/raw/%s/1' % stream_id.hex)
        self.assertEqual(bytes(response.content), bytes(key.secret))

    def test_raw_key_from_stream_invalid(self):
        """
        This test is to ensure when a raw key value is asked for using a stream ID and
        segement number, and the key doesn't exist, the correct error message is
        returned.
        """
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()

        # generate a key
        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'raw_key', stream_id.hex))
        self.assertContains(response, stream_id, count=1)
        self.assertEqual(Key.objects.count(), 1)

        response = self.auth_get('/key/raw/%s/%i' % (stream_id.hex, settings.segment_size + 10))  # no key for stream, segment too high
        self.assertContains(response, "No Key found", status_code=404)

    def test_get_keyconfigs(self):
        """
        This test is to ensure that a proper list of keyconfigs is returned.
        """
        kc = KeyConfig(name='taped_config', usage=KeyConfig.LIVE_KEY)
        kc.save()
        kc2 = KeyConfig(name='kc2', usage=KeyConfig.LIVE_KEY)
        kc2.save()
        kc3 = KeyConfig(name='kc4', usage=KeyConfig.LIVE_KEY)
        kc3.save()

        # get list of configs and make sure they match
        response = self.client.get('/keyconfigs/')
        data = json.loads(response.content.decode('ascii'))
        name_list = data['keyconfigs']
        self.assertEqual(len(name_list), KeyConfig.objects.count())
        for keyConf in KeyConfig.objects.all():
            self.assertIn(keyConf.name, name_list)

        # make sure the returned json matches the schema
        with open('pysrc/keystore_app/schemas/keyconfig.schema.json', 'r') as schemaFile:
            keyconfig_schema = json.load(schemaFile)
        jsonschema.validate(data, keyconfig_schema)  # will thrown an exception if its not valid

    def test_health(self):
        """ This test is to ensure that a properly formatted health json is returned. """
        response = self.client.get('/health')
        data = json.loads(response.content.decode())

        # make sure the returned json matches the schema
        with open('pysrc/keystore_app/schemas/health.schema.json', 'r') as schemaFile:
            health_schema = json.load(schemaFile)
        jsonschema.validate(data, health_schema)  # will thrown an exception if its not valid

    def test_precached_keys(self):
        """
        Test to ensure that when a key request comes in for a new content-id, a PreCachedKeys object is created,
        and will load keys when the load_cache is called.  Subsequent calls should get a key out of that cache.
        """
        kc = KeyConfig(name="precache_config", usage=KeyConfig.LIVE_KEY, rotation_method=KeyConfig.SEGMENT_ROT,
                pre_create_keys=2, band_id="precache_band")
        kc.save()

        # first request should generate a key, an create a PreCachedKeys object
        stream_id = uuid.uuid4()
        self.assertEqual(PreCachedKeys.objects.count(), 0)
        response = self.auth_get("/key/get/%s/%s/%s/1" % (kc.name, "foo_id", stream_id.hex))
        validate_response_json(response)
        self.assertEqual(PreCachedKeys.objects.count(), 1)

        # load the cache an make sure the right amount of keys were created
        precache = PreCachedKeys.objects.get()
        self.assertEqual(precache.content_id, "foo_id")
        self.assertEqual(precache.num_unused, 0)
        precache.load_cache()
        self.assertEqual(precache.num_unused, kc.pre_create_keys)

        # hack to get the 'next key' the precached object will give out, without actually removing it from PreCache
        next_key = Key.objects.filter(cached_by=precache).order_by("created").first()

        # next request should return the key from the cache
        response = self.auth_get("/key/get/%s/%s/%s/%i" % (kc.name, "foo_id", stream_id.hex, 1 + kc.seg_size))
        rsp_json = json.loads(response.content.decode())
        self.assertEqual(precache.num_unused, kc.pre_create_keys - 1)
        self.assertEqual(uuid.UUID(rsp_json["keys"][0]["key_id"]), next_key.id)
        self.assertEqual(rsp_json["media_id"], kc.band_id)


class SlingboxTests(KeystoreTestCase):
    """
    These are tests for functions that are used by external systems that contact the
    Keystore (mainly slingbox stuff).
    """

    def _validate_rsp_json(self, rsp):
        self.assertEqual(rsp.status_code, 200)
        rsp_json = json.loads(rsp.content.decode())
        self.assertTrue("key_id" in rsp_json)
        self.assertTrue("key" in rsp_json)
        self.assertTrue("expires" in rsp_json)
        self.assertTrue("providers" in rsp_json)
        self.assertEqual(len(rsp_json["providers"]), 1)
        self.assertEqual(Key.objects.count(), 1)
        key = Key.objects.get()
        self.assertEqual(rsp_json["key_id"], key.id.hex)
        self.assertEqual(rsp_json["key"], key.secret.hex())

    def test_create_key(self):
        kc = KeyConfig(name="taped_config", seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id="devenc009")
        kc.save()
        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()
        data = {"keyconfig": kc.name}
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self._validate_rsp_json(rsp)

    def test_fetch_key(self):
        """
        Used to test the valid happy path of fetching an existing key.
        """
        self.test_create_key()
        key = Key.objects.get()
        data = {"key_id": key.id.hex}
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self.assertEqual(Key.objects.count(), 1)
        self._validate_rsp_json(rsp)

    def test_bad_requests(self):
        data = {"foo": "bar"}
        # missing data fields
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self.assertEqual(rsp.status_code, 400)
        # not a post
        rsp = self.client.get("/key/slingbox")
        self.assertEqual(rsp.status_code, 404)
        # no such key
        data = {"key_id": uuid.uuid1().hex}
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self.assertEqual(rsp.status_code, 404)
        # no such keyconfig
        data = {"keyconfig": "foo"}
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self.assertEqual(rsp.status_code, 404)
        # bad key_id format
        data["key_id"] = 1
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type=JSON)
        self.assertEqual(rsp.status_code, 400)
        # wrong content-type, must be application/json
        self.test_create_key()
        data = {"keyconfig": KeyConfig.objects.get().name}
        rsp = self.client.post("/key/slingbox", json.dumps(data), content_type="application/text")
        self.assertEqual(rsp.status_code, 404)


class BulkKeyTests(KeystoreTestCase):
    def _generate_keys(self):
        num_keys = 10
        kc = KeyConfig(name="bulk_config", seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id="devenc009")
        kc.save()
        keys = []
        for _ in range(num_keys):
            use_key = Key.create(kc, "bulk")
            use_key.save()
            keys.append(use_key.id)
        return keys

    # pylint: disable=W0613
    @mock.patch('pysrc.keystore_app.tasks.local_tasks.generate_bulk_keys.delay',
                return_value=Mock(id=uuid.UUID('123456781234567812345678123f00d1').hex))
    def test_request_bulk(self, generate_bulk_mock):
        kc = KeyConfig(name="bulk_config", seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id="devenc009")
        kc.save()
        data = {"num_keys": 10, "key_config": kc.name}
        resp = self.client.post("/key/bulk/generate", json.dumps(data), content_type=JSON)
        self.assertEqual(resp.status_code, 200, 'Response was --> {!r}'.format(resp.content))
        self.assertEqual(resp.content.decode('UTF-8'), '"123456781234567812345678123f00d1"')

    # pylint: disable=W0613
    @mock.patch('celery.result.AsyncResult.state', create=True, new_callable=mock.PropertyMock, return_value="SUCCESS")
    def test_get_bulk(self, AsyncResult_state):
        bulk_request_id = str(uuid.uuid4())
        with mock.patch('celery.result.AsyncResult.result', create=True, new_callable=mock.PropertyMock,
                        return_value=self._generate_keys()):
            resp = self.client.get("/key/bulk/get/{}".format(bulk_request_id))
        self.assertEqual(resp.status_code, 200, 'Response was --> {!r}'.format(resp.content))

    # pylint: disable=W0613
    @mock.patch('celery.result.AsyncResult.state', create=True, new_callable=mock.PropertyMock, return_value="PENDING")
    def test_bad_request(self, generate_bulk_mock):
        kc = KeyConfig(name="bulk_config", seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id="devenc009")
        kc.save()
        # no key_config param
        data = {"num_keys": 10}
        resp = self.client.post("/key/bulk/generate", json.dumps(data), content_type=JSON)
        self.assertEqual(resp.status_code, 404)
        # no num_keys param
        data = {"key_config": kc.name}
        resp = self.client.post("/key/bulk/generate", json.dumps(data), content_type=JSON)
        self.assertEqual(resp.status_code, 400)
        # illegal num_keys value
        data = {"num_keys": -1, "key_config": kc.name}
        resp = self.client.post("/key/bulk/generate", json.dumps(data), content_type=JSON)
        self.assertEqual(resp.status_code, 400)
        # malformed JSON
        resp = self.client.post("/key/bulk/generate", '{"num_keys": 10, "key_config": "bulk_config"', content_type=JSON)
        self.assertEqual(resp.status_code, 400)
        # no such keyconfig
        data = {"num_keys": 10, "key_config": "i_dont_exist"}
        resp = self.client.post("/key/bulk/generate", json.dumps(data), content_type=JSON)
        self.assertEqual(resp.status_code, 404)

        # malformed UUID
        resp = self.client.get("/key/bulk/get/e37-4")
        self.assertEqual(resp.status_code, 404)
        # no such UUID
        resp = self.client.get("/key/bulk/get/{}".format(str(uuid.uuid4())))
        self.assertEqual(resp.status_code, 203)


class PreCacheTests(KeystoreTestCase):
    def test_lots_of_keys(self):
        """
        Test to ensure that when we go and get keys, we use the any precached ones first, but can still serve
        keys with no precached keys too.  If the precaching gets messed up, this function can take a LONG time
        to run.
        """
        TEST_KEYS = 1000
        kc = KeyConfig(name="precache_config", seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id="devenc009")
        kc.save()
        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()
        # generate a bunch of keys
        for _ in range(TEST_KEYS):
            self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', uuid.uuid4().hex))
        self.assertEqual(Key.objects.count(), TEST_KEYS)

        # ensure there is 1 PreCachedKey object
        self.assertEqual(PreCachedKeys.objects.count(), 1)

        # shouldn't have any cached keys yet, so load some up, and ensure we load the right number
        precache = PreCachedKeys.objects.get()
        self.assertEqual(precache.num_unused, 0)
        precache.load_cache()
        self.assertEqual(precache.num_unused, kc.pre_create_keys)

        # request keys, and make sure it uses the precached ones
        for _ in range(kc.pre_create_keys):
            self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', uuid.uuid4().hex))
        self.assertEqual(precache.num_unused, 0)


class EndpointTests(KeystoreTestCase):
    def test_key_usage_endpoint(self):
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        stream_id = uuid.uuid4()
        response = self.auth_get('/key/get/%s/%s/%s/10' % (kc.name, 'one_segment', stream_id.hex))
        validate_response_json(response)
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)

        expected_usage = KeyUsage.objects.first()
        expected_raw_key = expected_usage.key.secret.hex()
        expected_content_ids = []

        content_id = expected_usage.key.content_id
        # TODO: version 2.10 We used to set the stream id and content id to the 
        # user guid for rsdvr keys. Now we set the content id to 'rsdvr'. This 
        # check is to handle keys created before that change. At some point in 
        # the future we can remove this after all those keys are expired.
        if content_id == expected_usage.stream.id:
            content_id = "rsdvr"
        expected_content_ids.append(content_id)

        response_data = json.loads(response.content.decode(response.charset))
        for key in response_data['keys']:
            usage_response = self.auth_get('/key/usage/%s' % (key['key_id']))
            usage_data = json.loads(usage_response.content.decode(response.charset))
            self.assertEqual(usage_data['content_id'], expected_content_ids)
            self.assertEqual(usage_data['raw_key'], expected_raw_key)

    def test_key_usage_endpoint_rsdvr(self):
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        user_guid = uuid.uuid4()
        response = self.auth_get('/key/rsdvr/%s/%s' % (kc.name, user_guid))
        validate_response_json(response)
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)

        expected_usage = KeyUsage.objects.first()
        expected_raw_key = expected_usage.key.secret.hex()
        expected_content_ids = []

        content_id = expected_usage.key.content_id
        # TODO: version 2.10 We used to set the stream id and content id to the 
        # user guid for rsdvr keys. Now we set the content id to 'rsdvr'. This 
        # check is to handle keys created before that change. At some point in 
        # the future we can remove this after all those keys are expired.
        if content_id == expected_usage.stream.id:
            content_id = "rsdvr"
        expected_content_ids.append(content_id)

        response_data = json.loads(response.content.decode(response.charset))
        for key in response_data['keys']:
            usage_response = self.auth_get('/key/usage/%s' % (key['key_id']))
            usage_data = json.loads(usage_response.content.decode(response.charset))
            self.assertEqual(usage_data['content_id'], expected_content_ids)
            self.assertEqual(usage_data['raw_key'], expected_raw_key)

    def test_key_usage_endpoint_legacy_rsdvr_keys(self):
        """
        Tests that changes to content id for rsdvr will be compatible with keys 
        generated under the old method of setting content id to user guid
        TODO: version 2.10 we can remove this test at some point in the future 
        after all those keys are expired.
        """
        kc = KeyConfig(name='taped_config', seg_size=100, usage=KeyConfig.LIVE_KEY, lifetime=1, band_id='devenc009')
        kc.save()

        rel = KeyConfigRelation(key_config=kc, content_object=self.internal)
        rel.save()

        user_guid = uuid.uuid4()
        response = self.auth_get('/key/rsdvr/%s/%s' % (kc.name, user_guid))
        validate_response_json(response)
        self.assertEqual(Key.objects.count(), 1)
        self.assertEqual(KeyUsage.objects.count(), 1)

        expected_usage = KeyUsage.objects.first()
        expected_usage.key.content_id = str(user_guid);
        expected_usage.save()
        expected_raw_key = expected_usage.key.secret.hex()
        expected_content_ids = ['rsdvr']

        response_data = json.loads(response.content.decode(response.charset))
        for key in response_data['keys']:
            usage_response = self.auth_get('/key/usage/%s' % (key['key_id']))
            usage_data = json.loads(usage_response.content.decode(response.charset))
            self.assertEqual(usage_data['content_id'], expected_content_ids)
            self.assertEqual(usage_data['raw_key'], expected_raw_key)
