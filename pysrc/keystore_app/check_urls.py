from django.urls import path, re_path

import pysrc.keystore_app.views as views

# any standard programming name, can't start with dash, underscore, or number
CONFIG_PATTERN = '[A-Za-z][A-Za-z0-9_-]*'

urlpatterns = [
    # urls for checking if a provider is up
    re_path(r'^securemedia/(?P<timeout>\d+)$', views.check_securemedia, name='check_securemedia'),
    path('securemedia', views.check_securemedia, name='check_securemedia'),
    re_path(r'^playready/(?P<timeout>\d+)$', views.check_playready, name='check_playready'),
    path('playready', views.check_playready, name='check_playready'),
    re_path(r'^nagra/(?P<timeout>\d+)$', views.check_nagra, name='check_nagra'),
    path('nagra', views.check_nagra, name='check_nagra'),

    # urls for checking all the providers for a keyconfig
    re_path(r'^(?P<config_name>' + CONFIG_PATTERN + r')/(?P<timeout>\d+)$',
        views.check_keyconfig, name='check_keyconfig'),
    re_path(r'^(?P<config_name>' + CONFIG_PATTERN + ')/?$',
        views.check_keyconfig, name='check_keyconfig'),
]
