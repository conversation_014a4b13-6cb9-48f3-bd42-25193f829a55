# Generated by Django 2.2.6 on 2020-03-05 21:27

from django.db import migrations, models
import django.db.models.deletion
import pysrc.keystore_app.models.providers


class Migration(migrations.Migration):

    dependencies = [
        ('keystore_app', '0003_auto_20200228_2156'),
    ]

    operations = [
        migrations.AlterField(
            model_name='keyconfigrelation',
            name='content_type',
            field=models.ForeignKey(limit_choices_to=pysrc.keystore_app.models.providers.Provider.child_classes_query_set, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType', verbose_name='Provider Type'),
        ),
    ]
