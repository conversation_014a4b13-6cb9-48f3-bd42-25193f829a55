# Generated by Django 2.2.6 on 2020-02-28 21:56

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('keystore_app', '0002_slingdrmprovider'),
    ]

    operations = [
        migrations.RenameField(
            model_name='key',
            old_name='secret',
            new_name='_secret',
        ),
        migrations.AlterField(
            model_name='key',
            name='_secret',
            field=models.BinaryField(db_column='secret', verbose_name='Encryption Key'),
        ),
        migrations.AlterField(
            model_name='key',
            name='id',
            field=models.UUIDField(editable=False, primary_key=True, serialize=False, verbose_name='ID'),
        ),
        migrations.AlterField(
            model_name='keyconfigrelation',
            name='content_type',
            field=models.ForeignKey(limit_choices_to=models.Q(models.Q(('app_label', 'keystore_app'), ('model', 'primetimeprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'playreadyprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'securemediaprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'fairplayprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'internalprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'slingdrmprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'nagraprovider')), models.Q(('app_label', 'keystore_app'), ('model', 'widevineprovider')), _connector='OR'), on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType', verbose_name='Provider Type'),
        ),
    ]
