# -*- coding: utf-8 -*-
# Generated by Django 1.11.20 on 2019-06-27 23:28
from __future__ import unicode_literals

import datetime
from django.db import migrations, models
import django.db.models.deletion
import jsonfield.fields
import pysrc.keystore_app.models.keyconfig


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='FairPlayProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
            ],
            options={
                'verbose_name': 'FairPlay provider',
            },
        ),
        migrations.CreateModel(
            name='InternalProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
            ],
        ),
        migrations.CreateModel(
            name='Key',
            fields=[
                ('id', models.UUIDField(primary_key=True, serialize=False, verbose_name='ID')),
                ('secret', models.BinaryField(verbose_name='Encryption Key')),
                ('created', models.DateTimeField(auto_now_add=True, verbose_name='Created')),
                ('accessed', models.DateTimeField(auto_now_add=True, verbose_name='Last Accessed')),
                ('algorithm', models.CharField(choices=[('AES_128_CBC', 'AES-128 CBC'), ('DES3', 'Triple DES')], default='AES_128_CBC', max_length=20)),
                ('providers_meta', jsonfield.fields.JSONField(default=[])),
                ('content_id', models.CharField(default='', max_length=50)),
                ('has_all_meta', models.BooleanField(default=False, verbose_name='Has All Meta')),
            ],
        ),
        migrations.CreateModel(
            name='KeyConfig',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, validators=[pysrc.keystore_app.models.keyconfig.validate_keyconfig_name], verbose_name='Config Name')),
                ('usage', models.CharField(choices=[('vod', 'VoD'), ('live', 'Live')], default='live', max_length=20)),
                ('lifetime', models.PositiveIntegerField(blank=True, null=True, verbose_name='Key Lifetime (hrs)')),
                ('band_id', models.CharField(blank=True, max_length=20)),
                ('rotation_method', models.CharField(choices=[('segments', 'Segment Rotation'), ('time', 'Time Rotation')], default='segments', max_length=20)),
                ('seg_size', models.PositiveIntegerField(default=1800, validators=[pysrc.keystore_app.models.keyconfig.validate_seg_size], verbose_name='Rotation segment size')),
                ('rotation_time', models.TimeField(default=datetime.time(3, 0))),
                ('pre_create_keys', models.PositiveIntegerField(default=5, verbose_name='Keys to create before needed')),
                ('notes', models.CharField(blank=True, help_text='Free-form description of KeyConfig', max_length=200, verbose_name='Notes')),
            ],
        ),
        migrations.CreateModel(
            name='KeyConfigRelation',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.CharField(max_length=30, verbose_name='Provider Name')),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.ContentType', verbose_name='Provider Type')),
                ('key_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='keystore_app.KeyConfig')),
            ],
        ),
        migrations.CreateModel(
            name='KeyUsage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('startSeg', models.PositiveIntegerField(verbose_name='Start Segment')),
                ('endSeg', models.PositiveIntegerField(verbose_name='End Segment')),
                ('key', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='keystore_app.Key')),
            ],
        ),
        migrations.CreateModel(
            name='NagraProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
            ],
        ),
        migrations.CreateModel(
            name='PlayReadyProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
            ],
            options={
                'verbose_name': 'PlayReady provider',
            },
        ),
        migrations.CreateModel(
            name='PreCachedKeys',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content_id', models.CharField(max_length=50)),
                ('key_config', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='keystore_app.KeyConfig')),
            ],
            options={
                'verbose_name_plural': 'PreCached Keys',
                'verbose_name': 'PreCached Keys',
            },
        ),
        migrations.CreateModel(
            name='PrimetimeProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
                ('primetime_policy_id', models.CharField(default='SLING-TVI-LINEAR-QA', max_length=100, verbose_name='Policy ID')),
            ],
            options={
                'verbose_name': 'Primetime provider',
            },
        ),
        migrations.CreateModel(
            name='SecureMediaProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
                ('bands_url', models.URLField(blank=True, null=True, verbose_name='URL (bdremote)')),
            ],
        ),
        migrations.CreateModel(
            name='Stream',
            fields=[
                ('id', models.UUIDField(editable=False, primary_key=True, serialize=False, verbose_name='ID')),
                ('channel', models.CharField(blank=True, max_length=50)),
                ('content_id', models.CharField(blank=True, max_length=50)),
                ('encryption_block_size', models.PositiveIntegerField()),
                ('create_time', models.DateTimeField(blank=True, editable=False, null=True)),
                ('segment_duration', models.PositiveIntegerField(null=True)),
            ],
        ),
        migrations.CreateModel(
            name='WidevineProvider',
            fields=[
                ('name', models.CharField(max_length=30, primary_key=True, serialize=False, verbose_name='Provider Name')),
                ('url', models.URLField(blank=True, null=True, unique=True, verbose_name='Custom URL')),
                ('provider', models.CharField(max_length=100, verbose_name='Provider Key')),
            ],
            options={
                'verbose_name': 'Widevine provider',
            },
        ),
        migrations.AddField(
            model_name='keyusage',
            name='stream',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='keystore_app.Stream'),
        ),
        migrations.AddField(
            model_name='key',
            name='cached_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='keystore_app.PreCachedKeys'),
        ),
        migrations.AddField(
            model_name='key',
            name='config',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='keystore_app.KeyConfig'),
        ),
        migrations.AlterUniqueTogether(
            name='precachedkeys',
            unique_together=set([('key_config', 'content_id')]),
        ),
        migrations.AlterUniqueTogether(
            name='keyusage',
            unique_together=set([('stream', 'startSeg')]),
        ),
        migrations.AlterUniqueTogether(
            name='keyconfigrelation',
            unique_together=set([('key_config', 'content_type', 'object_id')]),
        ),
    ]
