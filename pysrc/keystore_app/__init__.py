# vim:softtabstop=4:ts=4:sw=4:expandtab:tw=120

import sys
import logging
import logging.handlers

from pysrc.configuration import KeystoreConf

__all__ = ["settings", "logger"]


def configure_logging():
    """
    Method used to setup the default python logger based on settings in the keystore.conf
    file.  The real problem is that when the handler is created, it forces the log file
    to be created, even if nothing needs to be logged yet.
    """
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')

    handler = logging.handlers.SysLogHandler('/dev/log')
    handler.setFormatter(formatter)

    logging.getLogger('keystore').addHandler(handler)
    logging.getLogger('keystore').setLevel(settings.log_level)


# create the settings object
settings = KeystoreConf()
logger = logging.getLogger('keystore')

# used to prevent logging from being configured when executing any of these setup commands
_skip_args = ['syncdb', 'collectstatic', "circusd"]
for arg in sys.argv:
    parts = arg.split('/')
    if parts[-1] in _skip_args:
        break
else:
    configure_logging()
