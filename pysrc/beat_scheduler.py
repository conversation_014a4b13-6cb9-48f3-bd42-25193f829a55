# pylint: disable=W0703
# coding: utf-8
# vim:softtabstop=4:ts=4:sw=4:expandtab:tw=120
import celery
import pika

from pysrc.keystore_app import logger as ks_log
from .configuration import KeystoreConf


class KeystoreCeleryBeatScheduler(celery.beat.PersistentScheduler):
    """
    Celery beat scheduler that is designed to be run with other redundant celerybeat servers running in the same
    datacenter, using rabbitmq as a way to perform sync locks.
    """
    def __init__(self, *args, **kwargs):
        super(KeystoreCeleryBeatScheduler, self).__init__(*args, **kwargs)
        self._connection = None
        self._channel = None
        self._setup_connection()

    def _setup_connection(self):
        """
        Used to establish a connection to the RabbitMQ server.  It is here instead of the constructor so
        it can be called when a connection problem is seen.  Don't use the _connection.connect method to
        reconnect, it doesn't seem to work right, instead just rebuild the connection from scratch.
        """
        _conf = KeystoreConf()
        _creds = pika.PlainCredentials(_conf.mq_user, _conf.mq_pass)
        try:
            self._connection = pika.BlockingConnection(pika.ConnectionParameters(
                host=_conf.mq_host,
                port=_conf.mq_port,
                virtual_host=_conf.mq_virtual,
                credentials=_creds)
            )
            self._channel = self._connection.channel()
        except Exception as ex:
            ks_log.error("Problem %s connecting to rabbitmq", ex)

    def maybe_due(self, entry, publisher=None):
        """
        Overrides the base implementation from celery.beat.Scheduler to do global locking with more than one
        celerybeat running.
        """
        is_due, next_time_to_run = entry.is_due()

        if is_due:
            ks_log.info('Scheduler: Sending due task %s (%s)', entry.name, entry.task)
            try:
                if self._get_global_lock():
                    self.apply_async(entry, publisher=publisher)
                else:
                    # we skipped this invocation because another admin celery beat process already did it,
                    # but still do the sync logic and update the entry's next instance the way that the
                    # apply_async method does
                    self.reserve(entry)
                    if self.should_sync():
                        self._do_sync()
            except pika.exceptions.ConnectionClosed:
                ks_log.error('Connection to rabbitmq server closed')
                self._setup_connection()
                next_time_to_run = 10  # retry in 10 seconds
            except Exception as ex:
                ks_log.error('Message Error: %s', ex)
                self._setup_connection()
                next_time_to_run = 10  # retry in 10 seconds
        return next_time_to_run

    def _get_global_lock(self):
        """
        Used to obtain a lock on the queue on the rabbitmq server.  It tries to obtain an exclusive lock on the
        queue, which will fail if another beat already has it locked.  It also uses auto_delete so that when this
        beat goes away (including crashes), the queue disappears from the server.
        @return A boolean indicating if a lock on the queue was obtained or not.
        """
        try:
            self._channel.queue_declare('keystore_celery_beat', auto_delete=True, exclusive=True)
            ks_log.debug('Obtained rabbitmq queue lock')
            return True
        except pika.exceptions.ChannelClosed:
            ks_log.debug('Failed to obtain rabbitmq queue lock')
        return False
