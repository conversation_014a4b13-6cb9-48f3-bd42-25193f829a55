from django.conf import settings
from django.contrib.auth.backends import Model<PERSON><PERSON>end
from django.contrib.auth.models import User
from django.contrib.auth.models import Group
from crowd import CrowdServer


class ModifiedCrowdBackend(ModelBackend):
    # pylint: disable=W0221
    def authenticate(self, username, password):
        """
        Authenticate a user with Crowd
        """
        crowd_config = getattr(settings, 'CROWD', None)
        if not crowd_config:
            raise UserWarning('CROWD configuration is not set in your settings.py')
        response = self._call_crowd(username, password, crowd_config)
        if response:
            user = self._find_existing_user(username)
            if user:
                user.set_password(password)
                user.save()
            else:
                user = self._create_user(username, password, response, crowd_config)
            return user
        else:
            return None

    def _call_crowd(self, username, password, crowd_config):
        """
        This was pulled out for testing purposes. Calls the Crowd server to authenticate a user
        """
        cs = CrowdServer(crowd_config['url'], crowd_config['app_name'], crowd_config['password'], ssl_verify=False)
        return cs.auth_user(username, password)

    def _find_existing_user(self, username):
        # pylint: disable=E1101
        users = User.objects.filter(username=username)
        if users.count() == 0:
            return None
        else:
            return users[0]

    def _create_user(self, username, password, content, crowd_config):
        """
        Creates a new user and adds it to the readonly group if not the first user
        """
        user = User.objects.create_user(username, content['email'], password)
        user.first_name = content['first-name']
        user.last_name = content['last-name']
        user.is_active = True
        if 'superuser' in crowd_config:
            user.is_superuser = crowd_config['superuser']
        if 'staffuser' in crowd_config:
            user.is_staff = crowd_config['staffuser']
        user.save()

        # pylint: disable=E1101
        num_of_users = User.objects.all().count()
        # First user gets superuser privilege and rest is readonly by default
        if num_of_users > 1:
            grp = Group.objects.get_or_create(name="readonly")
            grp[0].user_set.add(user)
        return user
