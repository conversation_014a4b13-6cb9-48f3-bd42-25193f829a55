#!/usr/bin/env python3
#pylint: disable=C0410,C0411,C0413

import os, sys
parent = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent not in sys.path:
    sys.path.insert(1, parent)
from pysrc import configuration as cfg

if __name__ == "__main__":
    try:
        cfg.KeystoreConf()
    except cfg.NoConfFile:
        arg = sys.argv[1:2][0] or ''
        message = "No %s file when executing manage '%s'\n" % (cfg.KEYSTORE_CONF_FILENAME, arg)
        print('FATAL ERROR:', message)
        import logging
        handler = logging.handlers.SysLogHandler('/dev/log')
        logging.getLogger('manage.py').addHandler(handler)
        logging.critical(message)
        sys.exit(1)

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "pysrc.keystore_srv.settings")
    from django.core.management import execute_from_command_line
    execute_from_command_line(sys.argv)
