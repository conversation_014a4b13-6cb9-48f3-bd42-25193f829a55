# noinspection PyUnresolvedReferences
from django.urls import path, re_path, include
# noinspection PyUnresolvedReferences
from django.views.generic.base import RedirectView
# noinspection PyUnresolvedReferences
from django.contrib import admin
admin.autodiscover()

import pysrc.keystore_app.views as ks_views
import pysrc.keystore_srv.views as views

urlpatterns = [
    path('grappelli/', include('grappelli.urls')),  # grappelli URLS

    path('keyconfigs/', include('pysrc.keystore_app.keyconfig_urls')),
    path('key_docs/', include('pysrc.keystore_app.doc_urls')),
    path('key/', include('pysrc.keystore_app.urls')),
    path('check/', include('pysrc.keystore_app.check_urls')),

    path('admin/', admin.site.urls),
    path('admin/uwsgi/', include('django_uwsgi.urls')),

    re_path(r'^_?status(?:\.json)?$', ks_views.status, name='status'),
    re_path(r'^$', RedirectView.as_view(url='/admin', permanent=True)),

    # handle asking for keystore version
    re_path(r'^version/?$', views.version, name='version'),

    # handle asking for keystore health
    re_path(r'^health/?$', views.health, name='health'),

    # ready handler
    re_path(r'^ready/?$', ks_views.ready, name='ready'),
    ]
