"""
Django settings for keystore_srv project.

For more information on this file, see
https://docs.djangoproject.com/en/1.6/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/1.6/ref/settings/
"""

# Build paths inside the project like this: os.path.join(BASE_DIR, ...)
import os
import sys
BASE_DIR = os.path.dirname(os.path.dirname(__file__))

from pysrc.configuration import KeystoreConf
keystoreConf = KeystoreConf()

# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/1.6/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'nakjtx15_hg+1o+!@c26@l7hu_rw^kum63z$siwxjqj5xd$td4'

# SECURITY WARNING: don't run with debug turned on in production!
#DEBUG = False  # NOTE: actual value set below from keystore_settings.sls via config file.
DEBUG = keystoreConf.debug

# template format for django 1.9
TEMPLATES = [ {
    "BACKEND": "django.template.backends.django.DjangoTemplates",
    "APP_DIRS": True,
    "OPTIONS": {
        "debug": True,
        "context_processors": [
            "django.contrib.auth.context_processors.auth",
            "django.contrib.messages.context_processors.messages",
            "django.template.context_processors.request",
            ]
        }
    } ]

ALLOWED_HOSTS = ['*']

if keystoreConf.enable_crowd:
    AUTHENTICATION_BACKENDS = (
        'django.contrib.auth.backends.ModelBackend',
        'pysrc.accounts.modified_crowd.ModifiedCrowdBackend',
    )
else:

    AUTHENTICATION_BACKENDS = (
        'django.contrib.auth.backends.ModelBackend',
    )

# Application definition
INSTALLED_APPS = (
    'django.contrib.contenttypes',
    'grappelli.dashboard',
    'grappelli',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_celery_results',
    'django_uwsgi',
    'pysrc.keystore_app',
    *(('autodynatrace.wrappers.django',) if keystoreConf.use_dynatrace else tuple()),
)

MIDDLEWARE = (
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    "pysrc.keystore_app.middleware.ExceptionMiddleware",
)

ROOT_URLCONF = 'pysrc.keystore_srv.urls'

# the url to get redirected to when accessing a page that requires you to be logged in first
LOGIN_URL = '/key/login'

WSGI_APPLICATION = 'pysrc.keystore_srv.wsgi.application'

# where all the migrations for different apps live.  We only include the 'auth' package
# so its migration files are stored with our source, instead inside the pyenv, which gets
# wiped every time we do an install.  If an 'app' isn't listed, it uses a default 'migrations'
# folder inside the app
MIGRATION_MODULES = {"auth": "pysrc.accounts.auth_migrations"}

if 'test' in sys.argv:
    import logging
    logging.disable(logging.CRITICAL)

# Database
if keystoreConf.database_engine == 'postgresql':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.postgresql_psycopg2',
            'NAME': keystoreConf.database_name,
            'USER': keystoreConf.database_user,
            'PASSWORD': keystoreConf.database_password,
            'HOST': keystoreConf.database_host,
            'PORT': keystoreConf.database_port,
            'DISABLE_SERVER_SIDE_CURSORS': True,
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(BASE_DIR, 'db.sqlite3'),
        },
    }

# crowd setup for authentication
CROWD = {
    'url': keystoreConf.crowd_url,  # values are from dynamux pillar
    'app_name': keystoreConf.crowd_app,
    'password': keystoreConf.crowd_password,
    'superuser': True,
    'staffuser': True,
}

# grappelli stuff
GRAPPELLI_ADMIN_TITLE = 'Keystore Administration'
GRAPPELLI_INDEX_DASHBOARD = 'pysrc.keystore_srv.dashboard.CustomIndexDashboard'

# Internationalization
# https://docs.djangoproject.com/en/1.6/topics/i18n/
LANGUAGE_CODE = 'en-us'
TIME_ZONE = 'UTC'
USE_I18N = True
USE_L10N = True
USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/1.6/howto/static-files/
STATIC_URL = '/static_files/'
STATIC_ROOT = '/var/www/keystore/static_files'
STATICFILES_DIRS = [('schemas', os.path.join(BASE_DIR, 'keystore_app', 'schemas'))]

TEST_OUTPUT_VERBOSE = 2
TEST_OUTPUT_DIR = './build/'
