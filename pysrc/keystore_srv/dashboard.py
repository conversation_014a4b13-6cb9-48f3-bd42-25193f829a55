#pylint: disable=W0105
"""
This file was generated with the customdashboard management command and
contains the class for the main dashboard.

To activate your index dashboard add the following to your settings.py::
    GRAPPELLI_INDEX_DASHBOARD = 'pysrc.dashboard.CustomIndexDashboard'
"""

from django.utils.translation import ugettext_lazy as _
from grappelli.dashboard import modules, Dashboard
#from grappelli.dashboard.utils import get_admin_site_name


class CustomIndexDashboard(Dashboard):
    """
    Custom index dashboard for www.
    """

    def init_with_context(self, context):
        #site_name = get_admin_site_name(context)

        # append a group for "Administration" & "Applications"
        """
        self.children.append(modules.Group(
            _('Group: Administration & Applications'),
            column=1,
            collapsible=True,
            children = [
                modules.AppList(
                    _('Administration'),
                    column=1,
                    collapsible=False,
                    models=('django.contrib.*',),
                ),
                modules.AppList(
                    _('Applications'),
                    column=1,
                    css_classes=('collapse closed',),
                    exclude=('django.contrib.*',),
                )
            ]
        ))
        """

        # append an app list module for "Applications"
        self.children.append(modules.AppList(
            _('Applications'),
            #collapsible=True,
            column=1,
            css_classes=('collapse closed',),
            exclude=('django.contrib.*',),
        ))

        # append an app list module for "Administration"
        self.children.append(modules.ModelList(
            _('ModelList: Administration'),
            column=1,
            collapsible=False,
            models=('django.contrib.*',),
        ))

        # append another link list module for "support".
        """
        self.children.append(modules.LinkList(
            _('Media Management'),
            column=2,
            children=[
                {
                    'title': _('FileBrowser'),
                    'url': '/admin/filebrowser/browse/',
                    'external': False,
                },
            ]
        ))
        """

        # append another link list module for "support".
        self.children.append(modules.LinkList(
            _('Documentation / Support'),
            column=2,
            collapsible=False,
            children=[
                {
                    'title': _('Keystore Schemas'),
                    'url': '../key_docs/schemas/',
                    'external': False,
                    'description': 'A page showing all the json schemas being used by the keystore'
                },
                {
                    'title': _('Keystore API (Webservice calls)'),
                    'url': '../key_docs/api/',
                    'external': False,
                    'description': 'A page showing all the available webservice calls offered by the keystore'
                },
                {
                    'title': _('Celery Flower'),
                    'url': '../flower/',
                    'external': False,
                    'description': 'A page showing all the celery workers and queues on the message broker'
                },
                {
                    'title': _('uWSGI operation'),
                    'url': '../admin/uwsgi/',
                    'external': False,
                    'description': 'A page showing the status of the uWSGI engine'
                },
                {
                    'title': _('Django Documentation'),
                    'url': 'http://docs.djangoproject.com/',
                    'external': True,
                },
                {
                    'title': _('Grappelli Documentation'),
                    'url': 'http://django-grappelli.readthedocs.org/en/latest/',
                    'external': True,
                },
                #{
                #    'title': _('Grappelli Google-Code'),
                #    'url': 'http://code.google.com/p/django-grappelli/',
                #    'external': True,
                #},
            ]
        ))

        # append a feed module
        """
        self.children.append(modules.Feed(
            _('Latest Django News'),
            column=2,
            feed_url='http://www.djangoproject.com/rss/weblog/',
            limit=5
        ))
        """

        # append a recent actions module
        self.children.append(modules.RecentActions(
            _('Recent Actions'),
            limit=5,
            collapsible=True,
            column=2,
        ))
