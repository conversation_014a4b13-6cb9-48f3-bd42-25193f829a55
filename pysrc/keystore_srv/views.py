# pylint: disable=W0613,E1120,E1123,broad-except
# Django packages
from django.http import HttpResponse

# System packages
import json, datetime

JSON_TYPE = "application/json"  # string constants so we don't fat-finger it all over the place

def version(request):
    """
    Version endpoint has been moved to the root route. See pysrc/keystore_srv/views.py
    """
    from django.http import HttpResponsePermanentRedirect
    return HttpResponsePermanentRedirect("key/version")


def health(request):
    """
    Method used to check the health of the keystore.  It sends a json response of {healthy： true}
    as long as the server is running.
    """
    result = {}
    result['healthy'] = True
    return create_generic_rsp(result, 200)


def create_generic_rsp(json_msg, status_code, cache_header=False):
    """
    Helper method for generating properly formatted messages to send back to the client.
    :param json_msg - The message to send back, in json.
    :param status_code - The status code to send back.
    :param cache_header - Whether a cache header should be attached or not.  The max-age is
        based on the 'status_code' passed in.
    """
    response = HttpResponse(json.dumps(json_msg), content_type=JSON_TYPE)
    if cache_header:
        if status_code == 202:
            response["Cache-Control"] = "public, max-age=5"
        else:
            # find out when the earliest key expires, and set the Cache-Control to that
            min_expires = datetime.datetime.now() + datetime.timedelta(weeks=4)
            for usage in json_msg.get("keys", {}):
                key_expires = datetime.datetime.strptime(usage["expires"], "%Y-%m-%d %H:%M:%S")
                if key_expires < min_expires:
                    min_expires = key_expires
            # handle case where there is only 1 key, like a /key/info/<guid> request
            if "key" in json_msg:
                key_expires = datetime.datetime.strptime(json_msg["expires"], "%Y-%m-%d %H:%M:%S")
                if key_expires < min_expires:
                    min_expires = key_expires
            expire_seconds = (min_expires - datetime.datetime.now()).total_seconds()
            response["Cache-Control"] = "public, max-age=%i" % expire_seconds
    response.status_code = status_code
    return response


def create_error_rsp(error_msg, status_code):
    """
    Helper method for generating properly formatted error messages to send back to the client.
    :param error_msg - The error message to send back, a string.
    :param status_code - The status code to send back.
    """
    from . import logger
    result = {'error': error_msg}
    response = HttpResponse(json.dumps(result), content_type=JSON_TYPE)
    response.status_code = status_code
    if status_code >= 500:
        logger.error("status %r: %r", status_code, error_msg)
        response["Cache-Control"] = "public, max-age=5"
    else:
        logger.warning("status %r: %r", status_code, error_msg)
        response["Cache-Control"] = "public, max-age=3600"  # hour
    return response
