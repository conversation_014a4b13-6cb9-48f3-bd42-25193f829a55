import os

# set the default Django settings module for the "celery" program.
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "pysrc.keystore_srv.settings")

from celery import Celery
app = Celery('keystore_srv')
app.config_from_object('django.conf:settings')

from ..configuration import KeystoreConf
conf = KeystoreConf()

from pysrc.keystore_app.tasks.schedule import periodic_schedule

app.conf.update(
    BROKER_URL='amqp://{0}:{1}@{2}:{3}/{4}'.format(
        conf.mq_user, conf.mq_pass, conf.mq_host, conf.mq_port, conf.mq_virtual),
    CELERY_TASK_SERIALIZER='json',
    CELERY_RESULT_SERIALIZER='json',
    CELERY_ACCEPT_CONTENT=['json'],
    CELERY_ENABLE_UTC=True,
    CELERY_IGNORE_RESULT=False,
    CELERY_TRACK_STARTED=True,
    CELERY_RESULT_BACKEND = 'django-db',
    CELERYBEAT_SCHEDULER='pysrc.beat_scheduler.KeystoreCeleryBeatScheduler',
    CELERYBEAT_SCHEDULE=periodic_schedule
)

# Override success loging so it only logs runtime, not results
from celery.app import trace
trace.LOG_SUCCESS = "Task %(name)s[%(id)s] succeeded in %(runtime)ss"
