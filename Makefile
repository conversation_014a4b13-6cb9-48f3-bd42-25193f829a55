# Install locations
PYENV := pyenv
OUT := build

# "Flag files" to know whether or not a rule needs to be done, i.e. if this file/directory exists, this target got built.
BUILD_FLAG := ${OUT}/etc
INSTALL_FLAG := ${PYENV}/var/run

# Values for debpkg files
PKGNAME := keystore
VERSIONSTRING := $(shell echo $(shell cat version.json) | sed 's/,\ /\./g;s/[][]//g;s/\"//g')
DATESTRING = $(shell date +'%a, %d %b %Y %H:%M:%S -0700')
DEB_PYENV := /opt/keystore/libexec/${PYENV}
DEB_EXAMPLE_FILES := /opt/keystore/etc
RUN_DIR := ${DEB_PYENV}/var/run
LOG_DIR := ${DEB_PYENV}/var/log
DEB_USER := keystore
DEB_GROUP := keystore
VARDIRS := run db tmp
BASEPATH := /opt/keystore


.PHONY: help configure build_dirs build check pylint dev_start dev_stop dev_restart debpkg_install_files debpkg clean

############################################################
# Help command
############################################################
help:
	@echo "Usage: make <command(s)>"
	@echo "Commands:"
	@echo "\thelp - Show this help message"
	@echo "\tconfigure - Set up the python virtualenv"
	@echo "\tbuild - Build the project"
	@echo "\tinstall - Install the built project into the python virtualenv"
	@echo "\tcheck <option(s)> - Run unit tests"
	@echo "\t\tOptions:"
	@echo "\t\tcoverage=1 - Generate test coverage reports"
	@echo "\t\txml-reports=1 - Generate XML test reports"
	@echo "\tpylint - Run pylint on the pysrc directory"
	@echo "\tdev_[start | stop | restart] - Starts/stops/restarts the keystore"
	@echo "\tdebpkg - Compile and build debian packages"
	@echo "\tclean - Clean the build"
	@echo
	@echo "Commands with prerequisites will run those prerequisistes if they haven't been run yet."
	@echo "For example, without running any other commands, 'make debpkg' will run the configure, build, and install commands first."
	@echo "If 'make install' has already been run, then 'make debpkg' does not run it again."

############################################################
# Configure/provision step
############################################################
local_salt/minion:
	./local_salt/make_minion_config.py

${PYENV}: | local_salt/minion
	sudo salt-call state.apply keystore.provision --config-dir=local_salt --log-level=info

configure: | ${PYENV} 

############################################################
# Build step
############################################################
build_dirs:
	sudo chown -R --from=root ${USER}:staff ${PYENV}
	mkdir -p ${OUT}/etc ${PYENV}/etc

${BUILD_FLAG}:
	${MAKE} build
build: | ${PYENV} build_dirs
	cp version.json ${OUT}/etc/version.json
	$(file >${OUT}/keystore_devmode.pth,${CURDIR}/pysrc)

############################################################
# Install step
############################################################
${INSTALL_FLAG}:
	${MAKE} install
install: | ${BUILD_FLAG}
	sudo salt-call state.apply keystore.setup_configs --config-dir=local_salt --log-level=info
	mkdir -p ${PYENV}/var/run
	cp ${OUT}/etc/* ${PYENV}/etc/
	cp ${OUT}/keystore_devmode.pth `${PYENV}/bin/python -c "from sysconfig import get_paths; print(get_paths()['purelib'])"`/
	sudo find ${PYENV}/bin -type f -exec chmod 775 {} +
	virtualenv --python=${PYENV}/bin/python ${CURDIR}/${PYENV}
	sudo salt-call state.apply db_server.keystore_db --config-dir=local_salt --log-level=info

############################################################
# Check/pylint commands
############################################################
check: | ${INSTALL_FLAG}
	@rm -f .coverage
ifeq (${coverage},1)
	$(eval cov_params = -m coverage run --source=pysrc/keystore_app)
endif
ifeq (${xml-reports},1)
	$(eval xml_params = --testrunner xmlrunner.extra.djangotestrunner.XMLTestRunner)
endif
	@echo "\033[32mRunning Keystore Unit Tests...\033[0m"
	@${PYENV}/bin/python ${cov_params} -m pysrc.manage test -v 2 ${xml_params}

# Gather any reports and display locations to the user
ifeq (${xml-reports},1)
	@echo
	@echo "\033[36m----------------------------------------------------------------------\033[0m"
	@echo "\033[36mXML reports are available in ${OUT}/*.xml\033[0m"
	@echo "\033[36m----------------------------------------------------------------------\033[0m"
endif
ifeq (${coverage},1)
	@${PYENV}/bin/python -m coverage xml -o ${OUT}/cov/coverage.xml
	@echo
	@echo "\033[36m----------------------------------------------------------------------\033[0m"
	@echo "\033[36mCoverage reports are available in ${OUT}/cov/coverage.xml\033[0m"
	@echo "\033[36m----------------------------------------------------------------------\033[0m"
	@rm -f .coverage
endif
	@echo
	@echo "\033[32mCheck Complete. All tests passed.\033[0m"

pylint:
	@echo "\033[36mRunning pylint on pysrc...\033[0m"
	@${PYENV}/bin/pylint -r n pysrc

############################################################
# Dev start/stop/restart commands
############################################################
dev_start: | ${INSTALL_FLAG}
	sudo salt-call state.apply keystore.dev_start --log-level=info --state-output=mixed --config-dir=local_salt

dev_stop: | ${INSTALL_FLAG}
	sudo salt-call state.apply keystore.dev_stop --log-level=info --state-output=mixed --config-dir=local_salt

dev_restart: | ${INSTALL_FLAG}
	${MAKE} dev_stop
	${MAKE} dev_start

############################################################
# Debpkg step
############################################################

# Will be called by debhelper in the "auto-install" step
debpkg_install_files:
	@echo "\033[36mModifying salt configuration for debian packaging...\033[0m"
	cp local_salt/minion local_salt/minion.old
	@echo "set_home_directory: ${CURDIR} --> ${RUN_DIR}"
	@sed -i "s/set_home_directory: $(subst /,\/,${CURDIR})/set_home_directory: $(subst /,\/,${RUN_DIR})/g" local_salt/minion
	@echo "set_pyenv_directory: ${CURDIR}/${PYENV} --> ${DEB_PYENV}"
	@sed -i "s/set_pyenv_directory: $(subst /,\/,${CURDIR}/${PYENV})/set_pyenv_directory: $(subst /,\/,${DEB_PYENV})/g" local_salt/minion
	@echo "set_etc_directory: ${CURDIR}/${PYENV}/etc --> /etc"
	@sed -i "s/set_etc_directory: $(subst /,\/,${CURDIR}/${PYENV}/etc)/set_etc_directory: \/etc/g" local_salt/minion
	@echo "set_home_directory: ${USER} --> ${DEB_USER}"
	@sed -i "s/set_linux_user: ${USER}/set_linux_user: ${DEB_USER}/g" local_salt/minion

	@echo "\033[36mBuilding pyenv in debian folder...\033[0m"
	mkdir -p debian/keystore${DEB_PYENV}
	cp -r ${PYENV}/* debian/keystore${DEB_PYENV}/
	rm debian/keystore${DEB_PYENV}/etc/keystore.conf
	cp -r pysrc `debian/keystore${DEB_PYENV}/bin/python -c "from sysconfig import get_paths; print(get_paths()['purelib'])"`/pysrc
	debian/keystore${DEB_PYENV}/bin/pip uninstall -y -r deps/uninstall.deps
	@echo "Changing ${PYENV} activation script."
	@sed -i "s/$(subst /,\/,${CURDIR}/${PYENV})/$(subst /,\/,${DEB_PYENV})/g" debian/keystore${DEB_PYENV}/bin/activat*
	@echo "Changing ${PYENV} bin shebangs."
	find debian/keystore${DEB_PYENV}/bin -type f ! -name "*.*" -exec sed -i "1s~$(subst /,\/,!${CURDIR}/${PYENV})~$(subst /,\/,!${DEB_PYENV})~" '{}' \;


	@echo "\033[36mBuilding configuration files...\033[0m"
	mkdir -p debian/keystore/etc/nginx/sites-enabled
	sudo salt-call state.apply keystore.deb_files --log-level=info --state-output=mixed --config-dir=local_salt pillar='{"current_directory": "${CURDIR}"}'
# Copy debian-specific files
	cp conf/logrotate.conf.in debian/keystore${DEB_PYENV}/etc/nginx_logrotate.conf
	cp conf/debian/keystore.links.in ${OUT}/keystore.links
	cp conf/debian/keystore.preinst.in ${OUT}/keystore.preinst
	cp conf/debian/keystore.postinst.in ${OUT}/keystore.postinst
	cp conf/debian/keystore.prerm.in ${OUT}/keystore.prerm
	cp conf/debian/keystore.postrm.in ${OUT}/keystore.postrm

	@echo "\033[36mUpdating values...\033[0m"
	@echo "@LOG_DIR@ -> ${LOG_DIR}"
	@sed -i "s/@LOG_DIR@/$(subst /,\/,${LOG_DIR})/g" debian/keystore${DEB_PYENV}/etc/nginx_logrotate.conf
	@echo "@PYENVPATH@ -> ${DEB_PYENV}"
	@sed -i "s/@PYENVPATH@/$(subst /,\/,${DEB_PYENV})/g" ${OUT}/keystore.*
	@echo "@SYSUSER@ -> ${DEB_USER}"
	@sed -i "s/@SYSUSER@/${DEB_USER}/g" ${OUT}/keystore.*
	@echo "@SYSGROUP@ -> ${DEB_GROUP}"
	@sed -i "s/@SYSGROUP@/${DEB_GROUP}/g" ${OUT}/keystore.*
	@echo "@VARDIRS@ -> ${VARDIRS}"
	@sed -i "s/@VARDIRS@/$(subst /,\/,${VARDIRS})/g" ${OUT}/keystore.*
	@echo "@BASEPATH@ -> ${BASEPATH}"
	@sed -i "s/@BASEPATH@/$(subst /,\/,${BASEPATH})/g" ${OUT}/keystore.*

	@echo "\033[36mCreating example config files...\033[0m"
	mkdir -p debian/keystore${DEB_EXAMPLE_FILES}
	cp debian/keystore/etc/keystore.conf debian/keystore${DEB_EXAMPLE_FILES}/keystore.conf.example
	cp debian/keystore/etc/varnish/ks-cache.vcl debian/keystore${DEB_EXAMPLE_FILES}/ks-cache.vcl.example
	cp debian/keystore/etc/newrelic.conf debian/keystore${DEB_EXAMPLE_FILES}/newrelic.conf.example

	mv local_salt/minion.old local_salt/minion

debpkg: | ${INSTALL_FLAG}
	rm -f ../*.deb ../*.changes
# Changelog is needed for dpkg-buildpackage to start
	cp conf/changelog.in ${OUT}/changelog
	@echo "@PKGNAME@ -> ${PKGNAME}"
	@sed -i "s/@PKGNAME@/$(subst /,\/,${PKGNAME})/g" ${OUT}/changelog
	@echo "@VERSIONSTRING@ -> ${VERSIONSTRING}"
	@sed -i "s/@VERSIONSTRING@/$(subst /,\/,${VERSIONSTRING})/g" `find ${OUT} -maxdepth 1 -type f`
	@echo "@DATESTRING@ -> ${DATESTRING}"
	@sed -i "s/@DATESTRING@/$(subst /,\/,${DATESTRING})/g" `find ${OUT} -maxdepth 1 -type f`

	@echo "\033[36mBuilding debian package...\033[0m"
	dpkg-buildpackage -b -tc -d -uc -us
	@echo "\033[32mDebpkg finished successfully!\033[0m"

############################################################
# Clean command
############################################################
clean:
	sudo rm -rf ${OUT}/ ${PYENV}/
	sudo rm -rf debian/keystore debian/.debhelper/ debian/*.debhelper debian/debhelper* debian/*.substvars debian/*.log debian/files
	sudo rm local_salt/minion || true
	find . -name "*.pyc" -type f -delete
