#!/bin/sh -e
. /usr/share/debconf/confmodule

# preinst script -
# debhelper adds logic to stop service if upgrading

sysuser=@SYSUSER@
sysgroup=@SYSGROUP@
pyenv=@PYENVPATH@

case "$1" in
    install)
        # create system group/user, and add the rsdvr user to the disk group so we have access to disks
        if ! getent group | grep -q "^$sysgroup:" ; then
            echo -n "Adding group $sysgroup.."
            addgroup --quiet --system $sysgroup
            echo "..done"
        fi

        if ! getent passwd | grep -q "^$sysuser:"; then
            echo -n "Adding system user $sysuser.."
            adduser --quiet --system --ingroup $sysgroup --no-create-home --disabled-password $sysuser
            adduser --quiet $sysuser disk
            echo "..done"
        fi
		if [ ! -e /var/log/keystore ]; then
			echo "creating /var/log/keystore"
			mkdir /var/log/keystore
		fi
    ;;

    upgrade)

    ;;

esac

#DEBHELPER#

