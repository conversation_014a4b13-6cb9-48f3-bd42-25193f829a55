#!/bin/sh -e
. /usr/share/debconf/confmodule

# postrm script - remove system accounts that we added

sysuser=@SYSUSER@
sysgroup=@SYSGROUP@
basepath=@BASEPATH@

case "$1" in 
   remove)
      if getent passwd | grep -q "^$sysuser:"; then
         echo -n "Removing system user $sysuser.."
         deluser --quiet --system $sysuser
         echo "..done"
      fi

      if getent group | grep -q "^$sysgroup:" ; then
         echo -n "Removing group $sysgroup.."
         delgroup --quiet --system $sysgroup
         echo "..done"
      fi

      if [ -d $basepath ] ; then
         rm -rf $basepath
      fi
   ;;

esac


#DEBHELPER#

