[Install]
#Wants runlevel [23]
WantedBy=multi-user.target 
#Conflicts runlevel [016] or shutdown

[Unit]
Description=KeyStore WebService
ConditionPathExists=|@PYENVPATH@/etc/keystore.conf
ConditionPathExists=|/etc/keystore.conf

[Service]
User=@SYSUSER@
# sometimes keystore crashes and failes to clean up these 
# socket files, which causes circus to fail to start 
ExecStartPre=-rm @PYENVPATH@/var/run/circus.sock
ExecStartPre=-rm @PYENVPATH@/var/run/keystore.sock
ExecStart=@PYENVPATH@/bin/circusd @PYENVPATH@/etc/circusd.conf
# this will block until is everything quits because of the --waiting
ExecStop=@PYENVPATH@/bin/circusctl --endpoint ipc://@PYENVPATH@/var/run/circus.sock quit --waiting
TimeoutStopSec=15
Restart=on-failure

