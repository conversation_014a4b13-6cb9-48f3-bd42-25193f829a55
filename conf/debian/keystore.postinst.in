#!/bin/sh -e
. /usr/share/debconf/confmodule

# postinst script - collect static files for the app, 
# - the DEBHELPER adds logic to restart service if upgrading

sysuser=@SYSUSER@
sysgroup=@SYSGROUP@
pyenv=@PYENVPATH@
vardirs="@VARDIRS@"
basedir=@BASEPATH@


if [ ! -d $pyenv/var ]; then
    mkdir $pyenv/var
fi

for dir in $vardirs; do
    if [ ! -d $pyenv/var/$dir ]; then
        mkdir $pyenv/var/$dir
    fi
done

# force the creation of the log file
#touch $pyenv/var/log/keystore.log

# make user own stuff, including the log file above
chown -R $sysuser:$sysgroup $pyenv/var
chown -R $sysuser:$sysgroup /var/log/keystore

${pyenv}/bin/python -m pysrc.manage collectstatic --link --noinput
#DEBHELPER#
