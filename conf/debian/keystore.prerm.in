#!/bin/sh -e
. /usr/share/debconf/confmodule
# prerm script - cleanup static files collected in postinstall
# debhelper adds logic to stop service when removing
pyenv=@PYENVPATH@

# run the debhelper first so the automatic stopping in the case of remove happens before we try to remove files
#DEBHELPER#

case "$1" in
       remove)
               if [ -d $pyenv/var ]; then
                       rm -rf $pyenv/var
               fi
               if [ -d /var/www/keystore ] ; then
                       rm -rf /var/www/keystore
               fi
               if [ -d $pyenv/lib/python3.5/site-packages/pysrc ] ; then
                       rm -rf $pyenv/lib/python3.5/site-packages/pysrc/*
               fi
       ;;
esac
