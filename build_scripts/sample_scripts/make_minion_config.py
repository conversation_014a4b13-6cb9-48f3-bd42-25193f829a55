#!/usr/bin/env python3
from pathlib import Path
import getpass
user = getpass.getuser()
cwd = Path('.')
mypath = Path(__file__).parent

template = """
# This file is used to configure a masterless minion
#
id: dynamux

file_client: local  # run as masterless

file_roots:    # use this directory rather than /srv/salt
  base:
    - {mypath}/salt    # use directory from dyno/dynamux/local_salt/salt
    - {cwd}/build_tools/salt   # then build_tools
    - {cwd}/lib/libqmx/local_salt/salt
    - {cwd}/lib/libsmuxed/local_salt/salt

top_file_merging_strategy: same  # do not merge the top.sls file from lib/salt
pillar_source_merging_strategy: recurse

file_ignore_regex:
  - '/\.git($|/)'

# state_output: mixed   # only print a single status line for GOOD states

grains:
  datacenter: standalone
  environment: masterless
  groups:
    - dynamux
  roles:
    - admin
    - cliplist
    - admin_mq_server
    - admin_db_server

fileserver_backend:
  - roots

pillar_roots:  # use this directory rather than /srv/pillar
  base:
    - {mypath}/pillar
    - {cwd}/lib/libqmx/local_salt/pillar
    - {cwd}/lib/libsmuxed/local_salt/pillar

worker_threads: 3

postgres.bins_dir: /usr/lib/postgresql/11/bin
set_postgres_version: "11"
set_home_directory: {cwd}
set_linux_user: {username}
set_linux_uid: ""
"""
try:
    if Path('/vagrant/local_salt/make_minion_config.py').exists():  # we are running on a Vagrant VM
        template += 'pyenv_var_run_link: /var/run/dynamux  # makes pyenv/var/run a symbolic link to /var/run\n'
except FileNotFoundError:
    pass
newtext = template.format(username=user, cwd=cwd.absolute(), mypath=mypath.absolute())
new_minion = mypath / 'minion'
new_minion.write_text(newtext)
print(__file__, 'created a new instance of', new_minion)
