# Build Scripts

generic tools to use for building packages 
-----------------

## INTRODUCTION

In order to replace things we were doing with `./waf` in the past,
we need a place to collect some handy shell scripts.

This repo will be the place.

## INSTALLATION
###as a submodule
You could include this repo in the usual way:   
in the home directory of your project...

`git submodule add ssh://***************************/dyno/build_scripts.git`

But, the utility of that is limited. Since the files here are not subject to frequent maintenance,
keeping a link to this repo might cause more problems than not -- so you may want a lighter approach.

###Or as a subtree

Suppose you just want a text copy of these files in your repo, without carrying around the maintenance history and etc. 

Consider using a [git subtree](https://www.atlassian.com/blog/git/alternatives-to-git-submodule-git-subtree).

In the home directory of your project...
```
git remote add -f build_scripts ssh://***************************/dyno/build_scripts.git
git subtree add --prefix ./build_scripts build_scripts master --squash
```

If you should wish to update these files from this repo run 
`build_scripts/udate_subtree.sh`.

In the unlikly event that you want to send an updated version of these files back to this repo, use a command like
`git subtree push --prefix=.build_scripts/ build_scripts <SOME_BRANCH_NAME_HERE>`.

## USAGE

Short version: Use `sudo salt-call --config-dir=local_salt foo bar` to run Salt commands masterless.

#### sample_scripts
A collection of scripts to copy elsewhere (your project's root directory), and modify for usage in your larger system.
- make_minion_config.py -- call from within your `provinios.sh` build script to create a 
stand-alone Salt Minion configuration file.
- provision.sh -- put a copy of this in your projects root directory to prepare your build machine for work.
- vgr -- (a bash script, but without the `.sh` so you can type it quicker) a quick script to run Vagrant in a parallel salt-bevy repo. 
Controls virtual machines without having all of the Vagrant cruft in your project space.
e.g. `./vgr up quail16` or `./vgr status`.

#### "pillar" and "salt" directories
`./build_scripts/salt` and `./build_scripts/pillar` are used to hold general-purpose salt states for provisioning your build machine.

#### Salt-bevy tools
Uses [https://github.com/salt-bevy/salt-bevy](https://github.com/salt-bevy/salt-bevy.git) (which was written here at Sling.)

Define and control a virtual machine, or a collection of VMs, to help build, test, and debug your stuff.
When started, each VM will map your project file as its `/vagrant` directory,
and the parent as `/projects` so that you can alter and run code without 
bouncing back-and-forth between machines. The files on each machine are actually the same file.
This is especially helpful if you are using remote debug in PyCharm Pro.

Lay out your project files in parallel with each other like:
```bash
my_projects_root_directory
|--- my_first_project
|     |--- build_scripts #(this repo)
|     |--- pysrc
|     |--- sample_scripts
|     |--- local_salt
|     |    |--- pillar
|     |    |--- salt
|     \--- and so forth
|
|--- salt_bevy
|     |--- Vagrantfile
|     |--- bevy_srv
|     |    |--- pillar
|     |    |--- salt
|     |--- configure_machine
|     |    \--- bootstrap_bevy_member_here.py
|     \-- and so forth
|--- some_other_project
     |--- and so forth
     
```
(Optionally, also add [https://github.com/salt-bevy/training](https://github.com/salt-bevy/training.git)
for a set of tutorials for Salt, salt-cloud, networking, and so on, using salt-bevy as a training tool.)

```bash
cp build_scripts/sample_scripts/* .
./vgr up quail2  #for example -- there is a largish collection of VMs to choose from
./vgr ssh quail2
```
and then, on quail2...
```bash
cd /vagrant
./provision.sh
make <whatever>
...
```

