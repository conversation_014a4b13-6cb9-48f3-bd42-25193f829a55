{% set python_version = salt['config.get']('python_version', 'python3') -%}
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) -%}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') -%}
#!{{ pyenv_path }}/bin/{{ python_version }}
# {{ pillar['salt_managed_message'] }}
"""
find all .py files in the project and run pylint on each, using the embedded copy of Python
"""
import os, sys, getpass
import pylint.lint


def find_pys(fbase):
    py_dirs = set()
    for f in os.listdir(fbase):
        if f in ('pyenv', 'build', '__pycache__', 'open_src', 'build_tools') or f.startswith('.'):  # skip generated directories
            continue
        fullpath = os.path.join(fbase, f)
        if f.endswith('.py'):
            py_dirs.add(fullpath)
            if f == "__init__.py":  # a package will be handled as a unit -- we don't need to look deeper
                continue
        elif os.path.isdir(fullpath):
            py_dirs |= find_pys(fullpath)  # recurse to find other python modules
    return py_dirs


switches = [s for s in sys.argv if s.startswith('-')]  # parse command line & supply defaults
args = [s for s in sys.argv[1:] if not s in switches] or ['.']
top = args.pop(0)
pylint_args = list(find_pys(os.path.abspath(top)))
if sys.version_info[0] == 2 and getpass.getuser() != 'build':  # skip python3 checks on build machine
    switches.append('--enable=python3,newstyle')  # look for Python3 compatibility
pylint_args.extend(args)
pylint_args.extend(switches)
print(' '.join(['pylint'] + pylint_args))
pylint.lint.Run(pylint_args)
