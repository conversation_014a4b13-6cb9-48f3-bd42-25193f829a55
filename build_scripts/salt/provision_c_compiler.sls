---
# salt state file for C compiler loading.

{% set compiler_name = salt['config.get']('compiler_name', 'gcc') %}
{% set compiler_version = salt['config.get']('compiler_version', '') %}

{# clang also needs gcc installed, so try to get the correct version for that...#}
{% set gcc_compiler_version = compiler_version %}
{% if compiler_name == 'clang' %}
  {% set gcc_compiler_version = '6' %}
{% endif %}

{% if grains['os_family'] == 'Debian' %}

  {% if gcc_compiler_version != '' %}
toolchain_gcc:
  pkgrepo.managed:
    - humanname: UbuntuToolchain
    - ppa: ubuntu-toolchain-r/test
    - keyserver: keyserver.ubuntu.com
    - keyid: BA9EF27F

libstdc++_apt_2:
  pkg.installed:
    - name: libstdc++6

{# install gcc compiler #}
gcc_install_{{ gcc_compiler_version }}:
  pkg.installed:
    - pkgs:
      - gcc-{{ gcc_compiler_version }}
      - g++-{{ gcc_compiler_version }}
    - require:
      - pkgrepo: toolchain_gcc

  {% else %}
gcc_install_default:
  pkg.installed:
    - pkgs:
      - gcc
      - g++
  {% endif %}


  {% if compiler_name == "clang" %}
    {% if compiler_version == '' %}  {# generic version #}
clang_install_generic:
  pkg.installed:
    - pkgs:
      - llvm-dev
      - clang
      - clang-format
    - unless:
      - which clang
    {% else %}
      {% if ((grains['os'] == "Ubuntu" and grains['osrelease'] < "16") or (grains['os'] == "Debian" and grains['osrelease'] < "8.2"))  and compiler_version > '3.8' %}
toolchain_clang_{{ compiler_version }}:
  pkgrepo.managed:
    - humanname: llvm Toolchain
    - name: "deb http://apt.llvm.org/{{ grains['oscodename'] }}/ llvm-toolchain-{{ grains['oscodename'] }}-{{ compiler_version }} main"
    - file: /etc/apt/sources.list.d/logstash.list
    - key_url: http://llvm.org/apt/llvm-snapshot.gpg.key
    - unless:
      - clang --version | grep {{ compiler_version }}
ignore_toolchain_error:
  test.nop:
    - onfail_in:
      - toolchain_clang_{{ compiler_version }}
    - require_in:
      - clang_install_{{ compiler_version }}
      {% endif %}
clang_install_{{ compiler_version }}:
  pkg.installed:
    - pkgs:
      - llvm-{{ compiler_version }}-dev
      - clang-{{ compiler_version }}
      - clang-format-{{ compiler_version }}
    - unless:
      - which clang-{{ compiler_version }}
    {% endif %}
  {% endif %}

{% elif grains['os'] == 'MacOS' %}
provision_c_mac:
  pkg.installed:
    - pkgs:
      - gcc
{% elif grains['os'] == 'Windows' %}
provision_c_win:
  chocolatey.installed:
    - name: mingw
{% endif %}
...
