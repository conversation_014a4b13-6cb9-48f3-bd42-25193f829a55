---
# salt state file for creating a waf compatible virtual environment with artifactory pypi mirror
{% set python_version = salt['config.get']('python_version', 'python3') %}
{% set user = salt['pillar.get']('my_linux_user', salt['environ.get']('SUDO_USER', salt['environ.get']('USER'))) %}
{% set art_url = salt['pillar.get']('artifactory_url', 'http://dishtechnology.pe.jfrog.io/artifactory/api/pypi/pypi-all') %}
{% set dyna_url = salt['pillar.get']('dyna_local_url', art_url | replace('pypi-all', 'dyna-pypi-local/simple') | replace('http:', 'https:')) %}
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') %}
{% set requirements = salt['file.find'](cwd + '/deps/*.deps') %}
prpyecho:
  {% if grains['os_family'] != 'Debian' %}
  test.fail_without_changes:
    - name: ERROR -- attempting to provision non-Linux operating system
    - failhard: True
    - order: 1
  {% else %}
  test.nop:
    - name: 'cwd={{ cwd }}  pyenv={{ pyenv_path }}  user={{user}}'
  {% endif %}

virtualenv_pkg:
  pkg.installed:
    - names:
      - virtualenv
      - python3-dev

create_venv:
  virtualenv.managed:
    - name: {{ pyenv_path }}
    - python: {{ python_version }}
    - system_site_packages: False
    - user: gitlab-runner
    - group: gitlab-runner
    - require:
      - virtualenv_pkg

set_pip_version:
  pip.installed:
    - name: pip==24.0
    - bin_env: {{ pyenv_path }}
    - require:
      - create_venv

{% for req in requirements %}
  {% if 'uninstall' not in req %}
add_requirement_{{ req }}:
  pip.installed:
    - bin_env: {{ pyenv_path }}
    - trusted-host: True
    - index-url: {{ art_url }}
    - extra-index-url: {{ dyna_url }}
    - requirements: {{ req }}
    - require:
      - set_pip_version
  {% endif %}
{% endfor %}

{{ cwd }}/.pylintrc:
  file.managed:
    - source: salt://{{ slspath }}files/pylintrc.jinja
    - template: jinja

{{ cwd }}/pylint:
  file.managed:
    - source: salt://{{ slspath }}files/pylint.py.jinja
    - template: jinja

{{ cwd }}/manage:
  file.managed:
    - contents:
        - "#!/bin/bash +x"
        - {{ pyenv_path }}/bin/{{ python_version }} {{ cwd }}/pysrc/manage.py "$@"

#{%  if salt['grains.get']('virtual', 'physical') != 'VirtualBox' or not pyenv_path.startswith('/projects')  %}
#    - mode: 776
#
#fix_build_ownership:
#  file.directory:
#    - name: {{ pyenv_path }}
#    - user: {{ user }}
#    - group: staff
#    - dir_mode: 775
#    - recurse: [user, group, mode]
#{%  endif %}
...
