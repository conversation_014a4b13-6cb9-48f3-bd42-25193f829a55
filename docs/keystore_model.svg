<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0" y="0" width="792" height="684" viewBox="0, 0, 792, 684">
  <g id="Layer_1">
    <text transform="matrix(1, 0, 0, 1, 358.994, 472.268)">
      <tspan x="-32.405" y="-81.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">Provider*</tspan>
      <tspan x="-112.129" y="-53.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">name : string</tspan>
      <tspan x="-112.129" y="-39.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">url : URL</tspan>
      <tspan x="-112.129" y="-25.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">ALLOWED_INSTANCES : </tspan>
      <tspan x="31.894" y="-25.5" font-family="CourierNewPS-BoldMT" font-size="12" fill="#000000">int</tspan>
      <tspan x="-112.129" y="2.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">to_dict() : json*</tspan>
      <tspan x="-112.129" y="16.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">check_connection(timeout : </tspan>
      <tspan x="82.303" y="16.5" font-family="CourierNewPS-BoldItalicMT" font-size="12" fill="#000000">int</tspan>
      <tspan x="103.906" y="16.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">;</tspan>
      <tspan x="-112.129" y="30.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">	url : string) : </tspan>
      <tspan x="31.09" y="30.5" font-family="CourierNewPS-BoldItalicMT" font-size="12" fill="#000000">bool*</tspan>
      <tspan x="-112.129" y="44.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">get_url() : string*</tspan>
      <tspan x="-112.129" y="58.5" font-family="CourierNewPS-ItalicMT" font-size="12" fill="#000000">get_type() : string*</tspan>
      <tspan x="-112.129" y="72.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">save()</tspan>
      <tspan x="-112.129" y="86.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">child_classes_query_set()</tspan>
    </text>
    <path d="M242.964,374.319 L476.024,374.319 L476.024,569.608 L242.964,569.608 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M242.964,400.015 L476.024,400.015" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M242.964,456.902 L476.024,456.902" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <text transform="matrix(1, 0, 0, 1, 129.673, 107.756)">
      <tspan x="-10.802" y="-75.25" font-family="CourierNewPSMT" font-size="12" fill="#000000">Key</tspan>
      <tspan x="-91.862" y="-47.25" font-family="CourierNewPSMT" font-size="12" fill="#000000">id : UUID</tspan>
      <tspan x="-91.862" y="-33.25" font-family="CourierNewPSMT" font-size="12" fill="#000000">secret : Blob</tspan>
      <tspan x="-91.862" y="-19.25" font-family="CourierNewPSMT" font-size="12" fill="#000000">created : DateTime</tspan>
      <tspan x="-91.862" y="-5.25" font-family="CourierNewPSMT" font-size="12" fill="#000000">accessed : DateTime</tspan>
      <tspan x="-91.862" y="8.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">algorithm : enum</tspan>
      <tspan x="-91.862" y="22.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">providers_meta : json</tspan>
      <tspan x="-91.862" y="36.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">config : KeyConfig</tspan>
      <tspan x="-91.862" y="50.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">content_id : string</tspan>
      <tspan x="-91.862" y="64.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">has_all_meta : </tspan>
      <tspan x="16.155" y="64.75" font-family="CourierNewPS-BoldMT" font-size="12" fill="#000000">bool</tspan>
      <tspan x="-91.862" y="78.75" font-family="CourierNewPSMT" font-size="12" fill="#000000">cached_by : PreCachedKeys</tspan>
    </text>
    <path d="M32.884,17.77 L227.462,17.77 L227.462,193.506 L32.884,193.506 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M32.884,43.187 L227.462,43.187" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <g>
      <text transform="matrix(1, 0, 0, 1, 376.669, 77.318)">
        <tspan x="-28.805" y="-32.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">KeyUsage</tspan>
        <tspan x="-69.645" y="-4.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">key : Key</tspan>
        <tspan x="-69.645" y="9.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">stream : Stream</tspan>
        <tspan x="-69.645" y="23.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">startSeg : uint</tspan>
        <tspan x="-69.645" y="37.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">endSeg : uint</tspan>
      </text>
      <path d="M302.251,31.675 L446.815,31.675 L446.815,120.318 L302.251,120.318 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M302.251,56.362 L446.815,56.362" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M236.462,79.236 L302.25,79.236" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M236.462,76.236 L228.462,79.236 L236.462,82.236 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 626.713, 85.766)">
        <tspan x="-21.604" y="-47.041" font-family="CourierNewPSMT" font-size="12" fill="#000000">Stream</tspan>
        <tspan x="-102.428" y="-19.041" font-family="CourierNewPSMT" font-size="12" fill="#000000">id : UUID</tspan>
        <tspan x="-102.428" y="-5.041" font-family="CourierNewPSMT" font-size="12" fill="#000000">channel : string</tspan>
        <tspan x="-102.428" y="8.959" font-family="CourierNewPSMT" font-size="12" fill="#000000">content_id : string</tspan>
        <tspan x="-102.428" y="22.959" font-family="CourierNewPSMT" font-size="12" fill="#000000">encryption_block_size : uint</tspan>
        <tspan x="-102.428" y="36.959" font-family="CourierNewPSMT" font-size="12" fill="#000000">create_time : DateTime</tspan>
        <tspan x="-102.428" y="50.959" font-family="CourierNewPSMT" font-size="12" fill="#000000">segment_duration : uint</tspan>
      </text>
      <path d="M519.161,24.744 L735.265,24.744 L735.265,145.695 L519.161,145.695 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M519.161,49.557 L735.265,49.557" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 130.394, 271.361)">
        <tspan x="-36.006" y="-18.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">Algorithms</tspan>
        <tspan x="-40.991" y="9.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">AES_128_CBC</tspan>
        <tspan x="-40.991" y="23.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">DES3</tspan>
      </text>
      <path d="M85.727,238.879 L174.62,238.879 L174.62,304.032 L85.727,304.032 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M85.727,263.693 L174.62,263.693" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 358.357, 262.14)">
        <tspan x="-32.405" y="-60.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">KeyConfig</tspan>
        <tspan x="-80.263" y="-32.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">name : string</tspan>
        <tspan x="-80.263" y="-18.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">seg_size : uint</tspan>
        <tspan x="-80.263" y="-4.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">usage : enum</tspan>
        <tspan x="-80.263" y="9.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">lifetime : uint</tspan>
        <tspan x="-80.263" y="23.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">band_id : string</tspan>
        <tspan x="-80.263" y="37.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">rotation_method : enum</tspan>
        <tspan x="-80.263" y="51.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">rotation_time : Time</tspan>
        <tspan x="-80.263" y="65.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">pre_create_keys : uint</tspan>
      </text>
      <path d="M273.62,185.495 L444.094,185.495 L444.094,337.337 L273.62,337.337 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M273.202,210.308 L444.512,210.308" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 522.042, 266.528)">
        <tspan x="-18.003" y="-18.888" font-family="CourierNewPSMT" font-size="12" fill="#000000">Usage</tspan>
        <tspan x="-19.861" y="9.112" font-family="CourierNewPSMT" font-size="12" fill="#000000">Live</tspan>
        <tspan x="-19.861" y="23.112" font-family="CourierNewPSMT" font-size="12" fill="#000000">VoD</tspan>
      </text>
      <path d="M497.304,231.89 L546.565,231.89 L546.565,298.915 L497.304,298.915 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M497.304,256.704 L546.565,256.704" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 194.741, 613.361)">
        <tspan x="-68.411" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">SecureMediaProvider</tspan>
      </text>
      <path d="M121.709,603.889 L268.773,603.889 L268.773,621.832 L121.709,621.832 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 355.744, 613.361)">
        <tspan x="-46.808" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">NagraProvider</tspan>
      </text>
      <path d="M301.232,603.889 L411.256,603.889 L411.256,621.832 L301.232,621.832 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 503.459, 613.597)">
        <tspan x="-57.609" y="2.264" font-family="CourierNewPSMT" font-size="12" fill="#000000">InternalProvider</tspan>
      </text>
      <path d="M443.715,603.889 L564.204,603.889 L564.204,621.832 L443.715,621.832 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M446.991,82.492 L508.697,82.492" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M508.697,85.492 L516.697,82.492 L508.697,79.492 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M130.307,193.506 L130.307,229.879" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M127.307,229.879 L130.307,237.879 L133.307,229.879 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M227.462,155.242 L266.093,180.561" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M264.448,183.07 L272.784,184.946 L267.737,178.052 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M359.275,337.5 L359.275,365.319" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M356.275,365.319 L359.275,373.319 L362.275,365.319 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M444.094,270.653 L487.647,270.653" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M487.647,273.653 L495.647,270.653 L487.647,267.653 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M359.494,589.608 L359.494,579.602" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M362.494,579.602 L359.494,571.602 L356.494,579.602 z" fill-opacity="0" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 349.442, 367.685)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">m</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 234.219, 148.695)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 277.085, 177.615)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 368.335, 345.127)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">n</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 453.735, 77.736)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">m</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 509.061, 96.082)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 295.736, 72.866)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 235.219, 90.779)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <g>
      <text transform="matrix(1, 0, 0, 1, 428.952, 651.643)">
        <tspan x="-61.21" y="2.264" font-family="CourierNewPSMT" font-size="12" fill="#000000">PlayReadyProvider</tspan>
      </text>
      <path d="M363.497,641.935 L495.407,641.935 L495.407,659.879 L363.497,659.879 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <g>
        <path d="M460.015,157.115 L608.915,157.115 L608.915,209.259 L460.015,209.259 L460.015,157.115 z" fill="#FFE755"/>
        <path d="M460.015,157.115 L608.915,157.115 L608.915,209.259 L460.015,209.259 L460.015,157.115 z" fill-opacity="0" stroke="#000000" stroke-width="0.5"/>
      </g>
      <text transform="matrix(1, 0, 0, 1, 533.965, 183.687)">
        <tspan x="-69.011" y="-15.5" font-family="CourierNewPSMT" font-size="10" fill="#000000">When someone requests a </tspan>
        <tspan x="-66.011" y="-4.5" font-family="CourierNewPSMT" font-size="10" fill="#000000">key, they are actually </tspan>
        <tspan x="-54.009" y="6.5" font-family="CourierNewPSMT" font-size="10" fill="#000000">getting info for a </tspan>
        <tspan x="-66.011" y="17.5" font-family="CourierNewPSMT" font-size="10" fill="#000000">particular "KeyUsage".</tspan>
      </text>
    </g>
    <g>
      <path d="M460.015,157.115 L448.841,125.966" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M445.115,121.861 C445.822,120.363 447.61,119.722 449.108,120.429 C450.606,121.136 451.248,122.924 450.541,124.422 C449.833,125.921 448.046,126.562 446.547,125.855 C445.049,125.148 444.407,123.36 445.115,121.861" fill-opacity="0" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <path d="M197.709,603.637 L197.709,589.608 L589.493,589.608 L589.493,642" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <g>
      <text transform="matrix(1, 0, 0, 1, 279.871, 651.643)">
        <tspan x="-57.609" y="2.264" font-family="CourierNewPSMT" font-size="12" fill="#000000">WidevineProvider</tspan>
      </text>
      <path d="M217.61,641.935 L343.133,641.935 L343.133,659.879 L217.61,659.879 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <text transform="matrix(1, 0, 0, 1, 129.673, 407.554)">
        <tspan x="-46.808" y="-18.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">PreCachedKeys</tspan>
        <tspan x="-79.862" y="9.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">key_config : KeyConfig</tspan>
        <tspan x="-79.862" y="23.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">content_id : string</tspan>
      </text>
      <path d="M45.384,374.319 L214.962,374.319 L214.962,440.054 L45.384,440.054 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M45.384,399.736 L214.962,399.736" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <g>
      <path d="M214.962,374.319 L266.007,342.137" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M267.607,344.675 L272.774,337.87 L264.407,339.599 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <g>
      <path d="M103,193.506 C103,193.506 63,216.5 63,279.5 C63,327.593 89.852,357.155 102.964,368.723" fill-opacity="0" stroke="#000000" stroke-width="1"/>
      <path d="M101.096,371.071 L109.225,373.702 L104.831,366.375 z" fill="#000000" fill-opacity="1" stroke="#000000" stroke-width="1" stroke-opacity="1"/>
    </g>
    <text transform="matrix(1, 0, 0, 1, 266.399, 328.5)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 215.399, 362.5)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">m</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 113.399, 364.5)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">1</tspan>
    </text>
    <text transform="matrix(1, 0, 0, 1, 81.626, 201.259)">
      <tspan x="-3.601" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">m</tspan>
    </text>
    <g>
      <text transform="matrix(1, 0, 0, 1, 587.139, 651.407)">
        <tspan x="-57.609" y="2.5" font-family="CourierNewPSMT" font-size="12" fill="#000000">FairPlayProvider</tspan>
      </text>
      <path d="M521.685,641.935 L653.594,641.935 L653.594,659.879 L521.685,659.879 z" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    </g>
    <path d="M285.25,589.5 L285.25,641.75" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M427.5,589.5 L427.5,641.75" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M504,589.5 L504,603.75" fill-opacity="0" stroke="#000000" stroke-width="1"/>
    <path d="M355,589.5 L355,603.75" fill-opacity="0" stroke="#000000" stroke-width="1"/>
  </g>
</svg>
