#!/usr/bin/make -f

%:
	dh $@ --with systemd

clean:
	sudo rm -rf debian/keystore debian/.debhelper/ debian/*.debhelper debian/debhelper* debian/*.substvars debian/*.log debian/files

override_dh_auto_build:
	@echo "Project already built"

override_dh_installinit:
	dh_installinit --name=keystore
	dh_installinit --name=keystore-celery
	dh_installinit --name=keystore-celerybeat
	dh_installinit --name=keystore-flower
	dh_installinit --name=keystore-uwsgi

override_dh_systemd_enable:
	dh_systemd_enable --name=keystore
	dh_systemd_enable --name=keystore-celery
	dh_systemd_enable --name=keystore-celerybeat
	dh_systemd_enable --name=keystore-flower
	dh_systemd_enable --name=keystore-uwsgi

override_dh_auto_install:
	make debpkg_install_files

override_dh_auto_test:
	make check xml-reports=1
