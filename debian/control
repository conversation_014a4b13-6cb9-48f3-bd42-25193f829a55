Source: keystore
Maintainer: Dynamux Team <<EMAIL>>
Section: python
Priority: optional
Build-Depends: debhelper (>= 9), dh-systemd (>= 1.5), python3-dev, libpq-dev, rabbitmq-server
Standards-Version: 3.7.2
XS-Python-Version: current

Package: keystore
Architecture: amd64
Depends: python3, postgresql-client, python3-psycopg2, nginx, uwsgi-plugin-python3
Description: KeyStore Server - Management of encryption keys
 This package is a python virtual environment that uses the 'django' library to create a webservice
 that other parties (encoder, dynamux, etc) can use to obtain keys for encrypting/decrypting .ts
 files. The keystore uses a database to store all it's needed information. It has been tested with
 sqlite and postgresql. Others may work, but haven't been tested.
 .
 Several different providers are available for different DRMs.  It currently has support for SecureMedia,
 Nagra, and Internal (the keystore itself).  As more DRMs are needed (PlayReady, FairPlay, etc), they
 can be added to the keystore as required.
 .
 An admin web interface is provided for viewing/editing the contents of the keystore. There is
 no default account for accessing this interface, but if you have a Crowd account you will be able
 to login to the admin.
 .
 For full details on how to setup/use the keystore, see the README.md file in 'stash'.
