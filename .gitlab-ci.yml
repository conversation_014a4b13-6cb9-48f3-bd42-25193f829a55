variables:
  BUILD_IMAGE: "dishtechnology-dyna-container.pe.jfrog.io/sre/key-store:${KEYSTORE_IMAGE_TAG}"
  RUNNER_TAG: "dyna"
  PIPELINE_APP: "keystore"
  DISTRO: "focal"
  APPS: "keystore"

include:
  - project: 'dish-cloud/sling/dyno/dyna-master-pipeline'
    ref: 'master'
    file: '/keystore/scripts.yaml'
  - project: 'dish-cloud/sling/dyno/dyna-master-pipeline'
    ref: 'master'
    file: '/.gitlab-ci-template.yaml'

.compile:
  script:
    - sudo apt-get clean
    - $CI_PROJECT_DIR/salt_me.sh
    - make clean
    - make debpkg
    - mv ../*.deb .
    - rm -rf $CI_PROJECT_DIR/pyenv

Start_Keystore_Deployment:
  extends: .trigger_deployment
  variables:
    APP_NAME: keystore
    OLD_VER: $OLD_VER
    SERVICE_COMPONENT: "dyna-drm-keystore"
  trigger:
    include:
      - artifact: child_pipeline_keystore.yaml
        job: Setup_Environment