#!/usr/bin/env python3
from pathlib import Path
import sys, getpass
user = getpass.getuser()
cwd = Path('.')
mypath = Path(__file__).parent

template = """
# This file is used to configure a masterless minion
#
id: keystore

file_client: local  # run as masterless

file_roots:    # use this directory rather than /srv/salt
  base:
    - {mypath}/salt    # use directory from keystore/local_salt/salt
    - {cwd}/build_scripts/salt   # then build_tools

top_file_merging_strategy: same  # do not merge the top.sls file from lib/salt
pillar_source_merging_strategy: recurse

file_ignore_regex:
  - '/\.git($|/)'

# state_output: mixed   # only print a single status line for GOOD states

grains:
  datacenter: standalone
  environment: masterless
  groups:
    - dynamux
  roles:
    - keystore
    - keystore_mq_server
    - keystore_db_server
application_name: keystore  # used to set the /run/< application_name > RuntimeDirectory in uWSGI configuration

no_load_sling_pkgs: True  # inhibit getting built applications from artifactory

fileserver_backend:
  - roots

pillar_roots:  # use this directory rather than /srv/pillar
  base:
    - {mypath}/pillar
    - {cwd}/build_scripts/pillar

worker_threads: 3

postgres.bins_dir: /usr/lib/postgresql/12/bin
set_postgres_version: "12"
set_home_directory: {cwd}
set_pyenv_directory: {pyenv}
set_etc_directory: {etc}  # where to look for '/etc' for this pyenv
set_linux_user: {username}
set_linux_uid: ""
python_version: "python3.{minor_version}"
"""
newtext = template.format(username=user, cwd=cwd.absolute(), mypath=mypath.absolute(), pyenv=(cwd / 'pyenv').absolute(),
                          etc=(cwd / 'pyenv' / 'etc').absolute(), minor_version=sys.version_info[1])
new_minion = mypath / 'minion'
new_minion.write_text(newtext)
print(__file__, 'created a new instance of', new_minion)
