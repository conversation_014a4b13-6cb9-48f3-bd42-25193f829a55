---
# salt state file for defining django database and users
#
# NOTE(1): must run on the "local" machine of the DB server, otherwise a password for the "postgres" user will be required
#  ...  so if you uncomment the db_host lines below, things will break.
#

include:
  - db_server.highstate

ks_db_user:
  postgres_user.present:
    - name: {{ salt['pillar.get']('keystore:db:user') }}
    - password: {{ salt['pillar.get']('keystore:db:password') }}
    - user: postgres
    - createdb: True
    - require:
      - service: postgresql

{% for user in pillar['keystore_postgres_read_only_users'] %}
ks_django_user_{{ user.name }}:
    postgres_user.present:
    - name: {{ user.name }}
    - password: {{ user.password }}
    - refresh_password: true
    - user: postgres
    - require:
      - service: postgresql

ks_django_ro_user_{{ user.name }}:
  cmd.run:
    - names:
      - psql -d postgres --no-readline --no-password -c "GRANT SELECT ON ALL TABLES IN SCHEMA public TO {{ user.name }}"
      - psql -d postgres --no-readline --no-password -c "ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO {{ user.name }}"
    - runas: postgres
    - require:
      - ks_django_user_{{ user.name }}
{% endfor %}

{% set do_reset = salt['config.get']('keystore_do_reset', false) %}
{% if do_reset %}
ks_kill_djangodb:
  postgres_database.absent:
    - name: {{ salt['pillar.get']('keystore:db:name') }}
    - user: postgres
    - require_in:
      - ks_djangodb
{% endif %}

ks_djangodb:
  postgres_database.present:
    - name: {{ salt['pillar.get']('keystore:db:name') }}
    - encoding: UTF8
    - template: template0
    - owner: {{ salt['pillar.get']('keystore:db:user') }}
    - user: postgres
    - require:
      - ks_db_user
...
