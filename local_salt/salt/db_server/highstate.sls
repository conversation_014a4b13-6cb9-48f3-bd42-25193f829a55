# vim:ff=unix ts=4 sw=4 ai expandtab
---
# Salt State file for: install postgres

{% set postgres_version = salt['pillar.get']('postgres_version', salt['config.get']('set_postgres_version', '9.5')) %}

{% if postgres_version != '9.5' %}
pg_repo:
  pkgrepo.managed:
    - humanname: postgresql_repo
    - name: deb http://apt.postgresql.org/pub/repos/apt/ {{ grains['oscodename'] }}-pgdg main
    - dist: {{ grains['oscodename'] }}-pgdg
    - file: /etc/apt/sources.list.d/psql.list
    - key_url: https://www.postgresql.org/media/keys/ACCC4CF8.asc
    - require_in:
      - pkg: postgresql
{% endif %}

postgresql:
  pkg:
    - installed
    - pkgs:
      - python3-psycopg2
      - postgresql-{{ postgres_version }}
      {% if postgres_version == '9.5' %}- postgresql-contrib-9.5 {% endif %}
      - postgresql-plpython3-{{ postgres_version }}
      - postgresql-server-dev-{{ postgres_version }}
  service.running:
    - enable: true
    - require:
      - pkg: postgresql
    - watch:
      - file: postgres-permissions
      - file: /etc/postgresql/{{ postgres_version }}/main/postgresql.conf

{% if salt['pillar.get']('keystore:db:use_connection') == 'pgpool2' %}
pgpool2:
  pkg:
    - installed
{% endif %}


postgres-permissions:
  # adding very open permissions.  TODO: restrict production databases
  file.prepend:
  - name: /etc/postgresql/{{ postgres_version }}/main/pg_hba.conf
  - text:
    - 'local all all trust  # added by Salt'
    - 'host all all 0.0.0.0/0 md5  # added by Salt'

/etc/postgresql/{{ postgres_version }}/main/postgresql.conf:
  file.append:    #  TODO: restrict production databases
  - text:
    - "listen_addresses = '*'  # Added by Salt"
    - "port = '{{ salt['pillar.get']('keystore:db:port', '5432') }}' # Added by Salt"
...
