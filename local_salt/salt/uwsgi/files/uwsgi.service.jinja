{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) -%}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') -%}
{% set etc = salt['config.get']('set_etc_directory', '/etc') -%}
{% set app = salt['config.get']('application_name', 'ERROR: application_name value not set in Salt config') %}
[Unit]
Description=uWSGI master service for {{ app }}
After=syslog.target
PartOf={{ app }}.service

[Service]
  {% if salt['pillar.get']('keystore:dynatrace:use_dynatrace', True) %}
Environment=AUTOWRAPT_BOOTSTRAP=autodynatrace
Environment=AUTODYNATRACE_FORKABLE=true
Environment=AUTODYNATRACE_CAPTURE_HEADERS=true
  {% endif %}

  {% if salt['pillar.get']('keystore:newrelic:use_newrelic', True) %}
Environment=NEW_RELIC_CONFIG_FILE=/etc/newrelic.conf
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/newrelic-admin run-program {{ pyenv_path }}/bin/uwsgi --master --ini=/etc/uwsgi/vassals/{{ app }}.ini'
  {% else %}
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/uwsgi --master --ini=/etc/uwsgi/vassals/{{ app }}.ini'
  {% endif %}

Restart=always
RestartSec=5
KillSignal=SIGQUIT
StandardError=syslog
Type=notify
NotifyAccess=all
#WorkingDirectory={{ cwd }}
#RuntimeDirectory={{ app }}
User={{ salt['config.get']('set_linux_user', 'www-data') }}
Group=www-data
#RuntimeDirectoryMode=777

[Install]
WantedBy=multi-user.target
