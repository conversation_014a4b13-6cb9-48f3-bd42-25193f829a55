# Salt State file for: uWSGI
# Author: vernon on 8/14/14 
{% set app = salt['config.get']('application_name', 'ERROR: application_name value not set in Salt config') %}

install_uwsgi:
  pkg.installed:
    - name: uwsgi-plugin-python3

/etc/uwsgi/vassals:
    file.directory:
    - makedirs: True

/etc/systemd/system/{{ app }}-uwsgi.service:
    file.managed:
        - source: salt://uwsgi/files/uwsgi.service.jinja
        - template: jinja

uwsgi-service:
    service.running:
        - enable: True
        - name: {{ app }}-uwsgi
        - watch:
          - file: /etc/uwsgi/vassals
          - file: /etc/uwsgi/uwsgi_params
          - file: /etc/systemd/system/{{ app }}-uwsgi.service
        - restart: True

/etc/uwsgi/uwsgi_params:
    file.managed:
      - makedirs: True
      - source: salt://uwsgi/files/uwsgi_params.original
