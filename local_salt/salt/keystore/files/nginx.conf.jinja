# vim:set ts=4 sw=4 sts=4 noexpandtab:
# {{ pillar['salt_managed_message'] }}
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}
{% set pyenv_path = salt['pillar.get']('keystore:pyenv_path') %}


upstream keystore-flower {
	server localhost:5555;
}

log_format ks_access '$remote_addr - $remote_user [$time_local] '
        '"$request" $status $body_bytes_sent '
        '"$http_referer" "$http_user_agent" '
		'rt=$request_time urt=$upstream_response_time';

server {
	listen {{ salt['pillar.get']('keystore:nginx_port') }};
	#server_name *;

	# log to syslog
	access_log syslog:server=unix:/dev/log,tag=nginx_ks_access,nohostname ks_access;
	error_log syslog:server=unix:/dev/log,tag=nginx_ks_error,nohostname;

	# also log to dedicated local logs
	access_log  /var/log/keystore/nginx-access.log ks_access;
	error_log  /var/log/keystore/nginx-error.log;

	location /static_files/ {
		root /var/www/keystore;
		try_files /$uri =404;
		access_log off;
	}

	location /flower/ {
#		rewrite ^/flower/(.*)$ /$1 break;
		proxy_pass http://keystore-flower;
		proxy_redirect default;
		proxy_set_header Host $host;
		proxy_set_header X-Real-IP $remote_addr;
		proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
		proxy_set_header X-Forwarded-Protocol http;
		proxy_set_header X-Forwarded-Host $host;
		proxy_buffering off;
		proxy_read_timeout 3600;
	}

    # Finally, send all non-media requests to the Django server.
    location / {
        uwsgi_pass {{ salt['pillar.get']('keystore:uwsgi_socket') }};
        include     /etc/uwsgi/uwsgi_params; # the uwsgi_params file Salt installed
        uwsgi_read_timeout 900;  # the time (in seconds) for django to return a response
    }
}
