{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) -%}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') -%}
{% set etc = salt['config.get']('set_etc_directory', '/etc') -%}

[Unit]
Description=Keystore Flower Service
After=network.target
PartOf=keystore.service

[Service]
Type=simple
User={{ salt['config.get']('set_linux_user', 'www-data') }}
Group=www-data
#WorkingDirectory={{ cwd }}
#RuntimeDirectory=keystore
#RuntimeDirectoryMode=777

  {% if salt['pillar.get']('keystore:dynatrace:use_dynatrace', True) %}
Environment=AUTOWRAPT_BOOTSTRAP=autodynatrace
Environment=AUTODYNATRACE_FORKABLE=true
Environment=AUTODYNATRACE_CAPTURE_HEADERS=true
  {% endif %}

ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/celery --app=pysrc.keystore_srv.celerysetup flower --url_prefix=flower --logging=INFO'
# if our service was stopped by KILL, stay stopped. otherwise, retry
Restart=on-failure
# wait a minute before retrying the service
RestartSec=60
# auto-restart process/watcher after 86400 seconds (1 day)
RuntimeMaxSec=86400

[Install]
WantedBy=multi-user.target
