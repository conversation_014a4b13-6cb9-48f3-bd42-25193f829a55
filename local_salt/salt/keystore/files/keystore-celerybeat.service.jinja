{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) -%}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') -%}
{% set etc = salt['config.get']('set_etc_directory', '/etc') -%}

[Service]
Type=forking

{# valid log-level values are DEBUG, INFO, WARNING, ERROR, CRITICAL, FATAL#}



[Unit]
Description=Celery Beat Service
After=celery.target
PartOf=keystore.service

[Service]
Type=simple
User={{ salt['config.get']('set_linux_user', 'www-data') }}
#WorkingDirectory={{ cwd }}
#RuntimeDirectory=keystore
#RuntimeDirectoryMode=777
# if our service was stopped by KILL, stay stopped. otherwise, retry
Restart=on-failure
# wait a while before retrying the service
RestartSec=90
# auto-restart process/watcher after 86400 seconds (1 day)
RuntimeMaxSec=86400

  {% if salt['pillar.get']('keystore:dynatrace:use_dynatrace', True) %}
Environment=AUTOWRAPT_BOOTSTRAP=autodynatrace
Environment=AUTODYNATRACE_FORKABLE=true
Environment=AUTODYNATRACE_CAPTURE_HEADERS=true
  {% endif %}

  {% if salt['pillar.get']('keystore:newrelic:use_newrelic', True) %}
Environment=NEW_RELIC_CONFIG_FILE={{ pyenv_path }}/etc/newrelic.conf
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/newrelic-admin run-program {{ pyenv_path }}/bin/celery --app=pysrc.keystore_srv.celerysetup beat --loglevel=INFO --schedule=/run/keystore/celerybeat-schedule --pidfile=/run/keystore/celerybeat.pid'
  {% else %}
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/celery --app=pysrc.keystore_srv.celerysetup beat --loglevel=INFO --schedule={{ pyenv_path }}/var/run/celerybeat-schedule --pidfile=/run/keystore/celerybeat.pid'
  {% endif %}

[Install]
WantedBy=multi-user.target
