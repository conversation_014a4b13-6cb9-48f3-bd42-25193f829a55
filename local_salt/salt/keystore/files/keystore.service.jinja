[Install]
#Wants runlevel [23]
WantedBy=multi-user.target 
#Conflicts runlevel [016] or shutdown

[Unit]
Description=KeyStore WebService
Wants=nginx.service keystore-uwsgi.service keystore-celery.service keystore-celerybeat.service keystore-flower.service
#ConditionPathExists=|/etc/keystore.conf

[Service]
# The dummy program will exit
Type=oneshot
# Execute a dummy program
ExecStart=/bin/true
# This service shall be considered active after start
RemainAfterExit=yes

