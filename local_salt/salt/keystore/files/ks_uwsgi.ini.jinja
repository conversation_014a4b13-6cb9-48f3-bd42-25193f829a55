# uWSGI initialization file
# {{ pillar['salt_managed_message'] }}
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}

[uwsgi]
#Generic Config
master = true

uid = www-data
enable-threads = true  # required for NewRelic and emporor mode. https://docs.newrelic.com/docs/agents/python-agent/web-frameworks-servers/python-agent-uwsgi-web-server
single-interpreter = true  # ^ ^ ^ ditto

# maximum number of processes
processes = {{ salt['pillar.get']('max_uwsgi_processes', grains['num_cpus']) }}

# N.O.T.E.  this socket must agree with the setting in nginx.conf.jinja
# best use http for local test server, unix socket for production
{% set socket = salt['pillar.get']('keystore:uwsgi_socket') %}
{% if 'unix' in socket %}
socket = {{ socket.split('://')[-1] }}
# with appropriate permissions - *may* be needed
chmod-socket = 666
{% else %}
socket = {{ socket }}
{% endif %}

# the base directory
chdir = {{ cwd }}

# Django's wsgi file
module = pysrc.keystore_srv.wsgi

logger = syslog:keystore_uwsgi
log-date = %%Y-%%m-%%dt%%H:%%M:%%S
