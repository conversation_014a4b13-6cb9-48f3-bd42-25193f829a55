{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) -%}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') -%}
{% set etc = salt['config.get']('set_etc_directory', '/etc') -%}
[Unit]
Description=Celery Service
After=network.target
PartOf=keystore.service

[Service]
Type=forking
User={{ salt['config.get']('set_linux_user', 'www-data') }}
#WorkingDirectory={{ cwd }}
#RuntimeDirectory=keystore
#RuntimeDirectoryMode=777
# if our service was stopped by KILL, stay stopped. otherwise, retry
Restart=on-failure
# wait 7 seconds before retrying the service
RestartSec=7
# auto-restart process/watcher after 86400 seconds (1 day)
RuntimeMaxSec=86400

  {% if salt['pillar.get']('keystore:dynatrace:use_dynatrace', True) %}
Environment=AUTOWRAPT_BOOTSTRAP=autodynatrace
Environment=AUTODYNATRACE_FORKABLE=true
Environment=AUTODYNATRACE_CAPTURE_HEADERS=true
  {% endif %}

  {% if salt['pillar.get']('keystore:newrelic:use_newrelic', True) %}
Environment=NEW_RELIC_CONFIG_FILE={{ pyenv_path }}/etc/newrelic.conf
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/newrelic-admin run-python {{ pyenv_path }}/bin/celery --app=pysrc.keystore_srv.celerysetup worker --loglevel=INFO -n keystore.%%h -c 1 &'
  {% else %}
ExecStart=/bin/sh -c '{{ pyenv_path }}/bin/celery --app=pysrc.keystore_srv.celerysetup worker --loglevel=INFO -n keystore.%%h -c 1 &'
  {% endif %}

[Install]
WantedBy=multi-user.target
