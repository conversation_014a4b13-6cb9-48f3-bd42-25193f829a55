{%- set my_env = salt['grains.get']('environment', 'unknown') -%}
{%- set my_stacks = salt['grains.get']('stacks', ['']) | join('|') -%}
[globals]
debug = {{ salt['pillar.get']('keystore:debug_mode', 'False') }}
segment_size = {{ salt['pillar.get']('keystore:deployment_segment_size') }}
require_auth = {{ salt['pillar.get']('keystore:require_authentication') }}
max_range_generation = {{ salt['pillar.get']('keystore:max_range_gen') }}
uwsgi_socket = {{ salt['pillar.get']('keystore:uwsgi_socket') }}

[crowd]
url = {{ salt['pillar.get']('dynamux:crowd:url') }}
app = {{ salt['pillar.get']('dynamux:crowd:app') }}
password = {{ salt['pillar.get']('dynamux:crowd:password') }}

[database]
[[common]]
engine = postgresql
connection_age = {{ salt['pillar.get']('keystore:db:connection_age') }}
name = {{ salt['pillar.get']('keystore:db:name') }}
user = {{ salt['pillar.get']('keystore:db:user') }}
password = {{ salt['pillar.get']('keystore:db:password') }}
connection_method = {{ salt['pillar.get']('keystore:db:use_connection') }}

[[direct]]
# nested section for direct database access
host = {{ salt['pillar.get']('keystore:db:cluster', 'localhost') }}{# cluster #}
port = {{ salt['pillar.get']('keystore:db:port') }}

{% for key, settings in salt['pillar.get']('keystore:db_connections', {}).items() -%}
[[{{ key }}]]
host = {{ settings['host'] }}
port = {{ settings['listen_port'] }}
{% endfor -%}

[providers]
kvhls_url = {{ salt['pillar.get']('drm_providers:securemedia_key_url') }}
kvhls_timeout = {{ salt['pillar.get']('drm_providers:securemedia_timeout') }}
# new url for getting bands from secure media
bdremote_url = {{ salt['pillar.get']('drm_providers:securemedia_band_url') }}
nagra_url = {{ salt['pillar.get']('drm_providers:nagra_url') }}
nagra_timeout = {{ salt['pillar.get']('drm_providers:nagra_timeout') }}
playready_url = {{ salt['pillar.get']('drm_providers:playready_url') }}
playready_timeout = {{ salt['pillar.get']('drm_providers:playready_timeout') }}
widevine_url = http://{{ salt['pillar.get']('drm_providers:widevine_vip') }}/widevine/proxy
widevine_timeout = {{ salt['pillar.get']('drm_providers:widevine_timeout') }}
fairplay_url = skd://{{ salt['pillar.get']('drm_providers:fairplay_vip') }}/fairplay/
fairplay_timeout = {{ salt['pillar.get']('drm_providers:fairplay_timeout') }}
primetime_url = http://localhost:8080/metadata
primetime_timeout = {{ salt['pillar.get']('drm_providers:primetime_timout') }}
primetime_policy_id = {{ salt['pillar.get']('drm_providers:primetime_policy_id') }}
slingdrm_url = {{ salt['pillar.get']('drm_providers:slingdrm_url', 'ERROR: slingdrm_url not defined in Pillar') }}
slingdrm_timeout = {{ salt['pillar.get']('drm_providers:slingdrm_timeout') }}

[logs]
log_level = {{ salt['pillar.get']('keystore:log_level') }}

[message_queue]
{% if salt['pillar.get']('keystore:message_queue:vip', none) is not none -%}
host = {{ salt['pillar.get']('keystore:message_queue:vip') }}{# vip defined in keystore pillar #}
{% else -%}
host = {{ salt['pillar.get']('keystore_mq_cluster', 'localhost') }}{# else use value from pillar_data.hosts #}
{% endif -%}
port = 5672  # default port, don't change unless server changes
user = {{ salt['pillar.get']('keystore:message_queue:user') }}
password = {{ salt['pillar.get']('keystore:message_queue:password') }}
virtual_host = {{ salt['pillar.get']('keystore:message_queue:vhost') }}

[monitoring]
use_newrelic = {{ salt['pillar.get']('keystore:newrelic:use_newrelic', False) }}
use_dynatrace = {{ salt['pillar.get']('keystore:dynatrace:use_dynatrace', False) }}
