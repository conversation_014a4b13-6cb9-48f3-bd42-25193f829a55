# See the vcl(7) man page for details on VCL syntax and semantics.
# if you want to watch the varnish log, run "varnishncsa" on the box running varnish
# keystore Varnish cache configuration
vcl 4.0;

import std;

## ACCESS CONTROL
acl purge {
{%- for item in salt['pillar.get']('keystore_varnish:purge', []) %}
    "{{ item }}";
{%- endfor %}
}


## HEALTH PROBES
probe ks_probe {
    #.url = "{{ pillar['keystore_varnish']['probe_vars']['url'] }}";
    .interval = {{ pillar['keystore_varnish']['probe_vars']['interval'] }};
    .timeout = {{ pillar['keystore_varnish']['probe_vars']['timeout'] }};
    .window = {{ pillar['keystore_varnish']['probe_vars']['window'] }};
    .threshold = {{ pillar['keystore_varnish']['probe_vars']['threshold'] }};
    # how to set a user agent on a probe
    .request = 
        "GET /health HTTP/1.1"
        "Host: localhost"
        "Connection: close"
        "User-Agent: keystore varnish-probe"
        "Accept-Encoding: */*";
}


## BACKEND DEFINITIONS
backend default {
    .host = "localhost";
    .port = "{{ salt['pillar.get']('keystore:nginx_port')  }}";  # nginx should be listening on this port
    .probe = ks_probe;
}


sub vcl_recv {
    # apply ACL to PURGE method
    if (req.method == "PURGE") {
        if (!client.ip ~ purge) {
            return (synth(403, "Access denied"));
        }
        return(purge);
    }
    # is the keystore healthy
    if (client.ip ~ purge && req.url == "/_status.json") {
        return (synth(200, "Backend Status"));
    }
}

sub vcl_backend_response {
    if (beresp.status == 202) {  # force varnish to cache 202 responses
        set beresp.ttl = std.duration(regsub(beresp.http.Cache-Control, ".*max-age=([0-9]+).*", "\1s"), 10s);
    }
}

sub vcl_synth {
    if (req.url ~ "^/_status.json" && resp.status == 200) {
        # create a json respose with the health of every single backend
        set resp.http.Content-Type = "application/json";
        synthetic({"{
            "default": "} + std.healthy(default) + {"
        }"});
    }
    else {
        synthetic(resp.reason);
    }
    return (deliver);
}
