# vim:ff=unix ts=4 sw=4 ai expandtab

# this file is intended to check a minion to ensure it is a keystore before applying
# any other states.  This is done to prevent accidental installs if someone fat-fingers
# a salt command (has already occurred, people installing a keystore on a dynapck box)

{% set my_roles = salt['grains.get']('roles', []) %}
{% if "keystore" not in my_roles and "test_keystore" not in my_roles %}
minion_not_a_keystore:
    test.fail_without_changes:
        - name: "Attempting to apply a keystore state on a non-keystore {{ my_roles }} box!!"
        - order: 1  # force this to be tested before other states
        - failhard: True
{% endif %}
