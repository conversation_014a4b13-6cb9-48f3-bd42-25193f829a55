# vim:ff=unix ts=2 sw=2 ai expandtab

# salt state file for starting nginx
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}
{% set pyenv_path = salt['config.get']('set_pyenv_directory') %}

kp_install_nginx:
  pkg.installed:
    - name: nginx

nginx.conf:
  file.managed:
    - name: /etc/nginx/sites-available/keystore
    - source: salt://keystore/files/nginx.conf.jinja
    - makedirs: True
    - template: jinja

start_system_nginx_1:
  file.symlink:
    - target: /etc/nginx/sites-available/keystore
    - name: /etc/nginx/sites-enabled/keystore
    - requires:
      - pkg: nginx
      - file: nginx.conf

kick_nginx:
  test.succeed_with_changes:
    - name: Make sure nginx does a reload

{{ pyenv_path }}/var/log:
  file.directory:  # make sure the log directory exists (on a test system)
    - makedirs: true

kp_finish_nginx_setup:
  file.absent:
    - names:
            - /etc/nginx/sites-enabled/default
  service:
    - running
    - name: nginx
    - order: last
    - watch:
      - start_system_nginx_1
      - kick_nginx
