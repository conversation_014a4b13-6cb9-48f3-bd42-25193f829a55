# vim:ff=unix ts=4 sw=4 ai expandtab

{% set my_env = salt['grains.get']('environment', 'unknown') %}
{% set my_stacks = salt['grains.get']('stacks', ['']) | join('|') %}
{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}
{% set pyenv = salt['pillar.get']('keystore:pyenv_path') %}

include:
    - keystore.minion_check
    - keystore.setup_configs
    - keystore.nginx
    - uwsgi

/etc/uwsgi/vassals/keystore.ini:
  file:
    - managed
    - makedirs: True
    - source: salt://keystore/files/ks_uwsgi.ini.jinja
    - template: jinja
    - require_in:
      - /etc/systemd/system/uwsgi.service

install_support_packages:
       pkg.installed:
        - pkgs:
            - postgresql-client

/run/keystore:
  file.directory:
    - user: 'keystore'
    - group: www-data
    - mode: 775

{% if grains['datacenter'] == 'standalone' or salt['config.get']('no_load_sling_pkgs') %}
keystore_user:
  user.present:
    - name: keystore  # created by "apt install" on an application server
    - system: True
    - optional_groups:
        - www-data
    - createhome: False
    - shell: /usr/sbin/nologin
    - require_in:
        - file: /run/keystore
{% else %}
install_keystore:
    pkg.installed:
        - skip_verify: True
        - name: keystore
        - require_in:
            - file: /etc/nginx/sites-enabled/keystore
        - version: "{{ salt['pillar.get']('keystore:version', 'latest') }}"
{% endif %}

create_keystore_static_files:
    cmd.run:  {# this is done by debian post-install on production systems #}
        - cwd: {{ cwd }}
        - name: "{{ pyenv }}/bin/python -m pysrc.manage collectstatic --link --noinput"

{% set migration_server = salt['pillar.get']('keystore:db:migration_server') %}

# only the migration server gets to run the migration
{% if migration_server in [grains['fqdn'], 'localhost'] %}
create_keystore_tables:
    cmd.run:
        - name: "{{ pyenv }}/bin/python -m pysrc.manage migrate --noinput"
        - user: "keystore"
        - cwd: {{ cwd }}
{% else %}
create_keystore_tables_pause:
    cmd.run:
        - name: sleep 20
        - require_in:
              - start_services
{% endif %}

{# service files are installed by Debian package on production machines #}
/etc/systemd/system/keystore.service:
    file.managed:
        - source: salt://keystore/files/keystore.service.jinja
        - mode: 644
        - template: jinja

/etc/systemd/system/keystore-celery.service:
    file.managed:
        - source: salt://keystore/files/keystore-celery.service.jinja
        - mode: 644
        - template: jinja

/etc/systemd/system/keystore-celerybeat.service:
    file.managed:
        - source: salt://keystore/files/keystore-celerybeat.service.jinja
        - mode: 644
        - template: jinja

/etc/systemd/system/keystore-flower.service:
    file.managed:
        - source: salt://keystore/files/keystore-flower.service.jinja
        - mode: 644
        - template: jinja

start_services:
  service.running:
    - names:  # make sure services are enabled
        - keystore-celery
        - keystore-celerybeat
        - keystore-flower
        - keystore
    - enable: True
    - watch:
      - kick_nginx
