---
# This provisions a build machine. A keystore production server should be different.

provision:
  pkg.installed:
    - pkgs:
      - chrpath
    {% if grains['os_family'] == 'Debian' %}
      - debhelper
      - libpq-dev
      - libtool
      - libxml2-dev
      - libxslt1-dev
      - pkg-config
      - dh-python
      - python3-pip
      {% if grains['osrelease'] < '18' %}
      - git-core
      - python-pip
      {%  endif %}
      - dh-systemd
      - python3-psycopg2
    {% endif %}
    - require_in:
        - create_venv

include:
  - python3_dev
  - provision_c_compiler
  - provision_pyenv
