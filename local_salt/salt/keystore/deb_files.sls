# Salt state for loading configuration and other required files into the debian folder for packaging.

{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('current_directory'))) %}
{% set deb_pyenv = salt['file.normpath'](cwd + '/debian/keystore/opt/keystore/libexec/pyenv') %}

{{ cwd }}/debian/keystore/etc/keystore.conf:
    file.managed:
        - makedirs: True
        - source: salt://keystore/files/keystore.conf.jinja
        - template: jinja

{{ cwd }}/debian/keystore/etc/newrelic.conf:
    file.managed:
        - makedirs: True
        - source: salt://keystore/files/newrelic.conf.jinja
        - template: jinja

{{ cwd }}/debian/keystore/etc/uwsgi/uwsgi_params:
    file.managed:
      - makedirs: True
      - source: salt://uwsgi/files/uwsgi_params.original

{{ cwd }}/debian/keystore/etc/uwsgi/vassals/keystore.ini:
    file.managed:
        - makedirs: True
        - source: salt://keystore/files/ks_uwsgi.ini.jinja
        - template: jinja

{{ cwd }}/debian/keystore/etc/varnish/ks-cache.vcl:
    file.managed:
        - makedirs: true
        - source: salt://keystore/files/ks-cache.vcl.jinja
        - template: jinja

{{ cwd }}/debian/keystore/etc/nginx/sites-available/keystore:
    file.managed:
        - makedirs: True
        - source: salt://keystore/files/nginx.conf.jinja
        - template: jinja

{{ cwd }}/build/keystore.service:
    file.managed:
        - source: salt://keystore/files/keystore.service.jinja
        - template: jinja

{{ cwd }}/build/keystore-uwsgi.service:
    file.managed:
        - source: salt://uwsgi/files/uwsgi.service.jinja
        - template: jinja

{{ cwd }}/build/keystore-celery.service:
    file.managed:
        - source: salt://keystore/files/keystore-celery.service.jinja
        - template: jinja

{{ cwd }}/build/keystore-celerybeat.service:
    file.managed:
        - source: salt://keystore/files/keystore-celerybeat.service.jinja
        - template: jinja

{{ cwd }}/build/keystore-flower.service:
    file.managed:
        - source: salt://keystore/files/keystore-flower.service.jinja
        - template: jinja
