# vim:ff=unix ts=2 sw=2 ai expandtab

{% set pyenv_path = salt['config.get']('set_pyenv_directory') %}

include:
  - db_server.keystore_db
  - uwsgi
  - keystore_mq_server.highstate
  - keystore.deploy

start_auxillary_services:  {# NOTE: "systemctl start keystore" will also START these, but will not enable them. #}
  service.running:
    - names:
        - keystore-celery
        - keystore-celerybeat
        - keystore-flower
        - keystore-uwsgi
    - enable: True
