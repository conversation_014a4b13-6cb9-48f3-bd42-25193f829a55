# vim:ff=unix ts=4 sw=4 ai expandtab
{% set pyenv_path = salt['pillar.get']('keystore:pyenv_path') -%}
include:
    - keystore.minion_check

{{ pyenv_path }}/etc/keystore.conf:
    file.managed:
        - source: salt://keystore/files/keystore.conf.jinja
        - makedirs: True
        - mode: 644
        - template: jinja

{{ pyenv_path }}/etc/newrelic.conf:
    file.managed:
        - source: salt://keystore/files/newrelic.conf.jinja
        - mode: 644
        - makedirs: True
        - template: jinja

