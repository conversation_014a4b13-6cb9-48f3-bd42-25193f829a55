# vim:ff=unix ts=2 sw=2 ai expandtab
---
stop_keystore:
  service.dead:
    - name: keystore
    - enable: false

cleanup_and_stop_nginx:
  service.dead:
    - name: nginx
    - enable: false
  file.absent:
    - name: /etc/nginx/sites-enabled/keystore

stop_rabbitmq-server:
  service.dead:
    - name: rabbitmq_server
    - enable: false

stop_auxillary_services:  {# NOTE: "systemctl stop keystore" will also STOP these, but will not disable them. #}
  service.dead:
    - names:
        - keystore-celery
        - keystore-celerybeat
        - keystore-flower
        - keystore-uwsgi
    - enable: false
...
