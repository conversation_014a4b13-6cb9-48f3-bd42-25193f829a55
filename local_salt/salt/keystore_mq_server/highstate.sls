# vim:ff=unix ts=4 sw=4 ai expandtab

rabbitmq-server:
    pkg.installed:
        - refresh: True

create_vhost:
    rabbitmq_vhost.present:
        - name: {{ salt['pillar.get']('keystore:message_queue:vhost') }}
        - require:
            - start_rabbit_ks

create_user:
    rabbitmq_user.present:
        - name: {{ salt['pillar.get']('keystore:message_queue:user') }}
        - password: {{ salt['pillar.get']('keystore:message_queue:password') }}
        - force: True
        - tags: "administrator"
        - perms:
            - '/':
                - '.*'
                - '.*'
                - '.*'
            - '{{ salt['pillar.get']('keystore:message_queue:vhost') }}':
                - '.*'
                - '.*'
                - '.*'
        - require:
            - pkg: rabbitmq-server
            - create_vhost

enable_plugins:
    rabbitmq_plugin.enabled:
        - name: rabbitmq_management
        - require:
            - pkg: rabbitmq-server

start_rabbit_ks:
    service.running:
        - name: rabbitmq-server
        - require:
            - pkg: rabbitmq-server
