#fs.inotify.max_user_watches:
#  sysctl.present:
#    - value: 1048576

#pkg.upgrade:
#  module.run:
#    - refresh: True
#    - order: 2
{% if grains['os'] == 'Ubuntu' %}
common_packages:
  pkg.installed:
    - pkgs:
      - htop
      - strace
      - vim-tiny
      - mtr
      - tree
      - git
      {% if grains['osrelease'] < '16.04' %}
      - python-git  # fallback package if pygit2 is not found.
      {% elif grains['pythonversion'][0] == 3 %}
      - python3-pygit2
      {% else %}
      - python-pygit2
      {% endif %}
      {% if grains['osrelease'] < '18.04' %}
      - python-software-properties
      {% endif %}
      {% if grains['locale_info']['defaultlanguage'] != 'en_US' %}
      - 'language-pack-en'
      {% endif %}
{% endif %}

remove_the_competition:  # these take a lot of virtual memory.
  pkg.removed:
    - names:
      - puppet
      - chef
{% if salt['config.get']('file_client') == 'local' %}  # if running masterless
  service.dead:  # sometimes the minion will start when it should not
    - name: salt-minion
{% endif %}

pkg.autoremove:
  module.run:
    - order: last
