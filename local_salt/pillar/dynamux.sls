# vim:ff=unix ts=2 sw=2 ai expandtab
# salt pillar file for definitions specific to dynamux.

  {# N.O.T.E...  this is a subset of the REAL dynamux.sls file #}

{# the following configuration values are usually set in the local Salt-minion configuration file
    ( which is called <project_home_directory>/local_salt/minion )
    which was (or will be) automatically generated when you ran (run) "make configure".
    They can be edited there, and will be preserved unless make is reconfigured with a --clang= or --gcc= switch
    #}
{% set project_home_directory = salt['config.get']('set_home_directory') %}
{% set postgres_version = salt['config.get']('set_postgres_version') %}
{% set pyenv_path = salt['config.get']('set_pyenv_path', project_home_directory + '/pyenv') %}
{% set pglogdir = '/var/log/postgres' %}

# directory where dynamux source is to be deployed
dynamux_home_path: {{ project_home_directory }}
make_home_path:     {{ project_home_directory }}
pyenv_path:        {{ pyenv_path }}

# a handy variable to use when upgrading PostgreSql.  Set the version in the salt config file and all else should follow.
postgres_version: '{{ postgres_version }}'

# - v - v - v - v - v - v - v - v - v - v - v - v - v - v - v - v -
# adapted from stash pillar_data  -- V.Cole
dynamux:
    pyenv_path:   {{ pyenv_path }}
    debug_mode:   yes
    "repo_server": "dishtechnology.pe.jfrog.io"
    "repo_url": "/artifactory/dyna-debian-local"
    "repo_env": "/"
    "repo_key_url": "/artifactory/api/gpg/key/public"

    "smtp_settings":
        "host": "mailrelay.movetv.com"
        # leave username and password blank if no authentication is required
        "user": ""
        "password": ""

    "crowd":
        "url": "https://crowd.imovetv.com/crowd"
        "app": "dynamux-dev"
        "password": "TV762677FG148019OI285507"

# - ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^- ^ - ^
