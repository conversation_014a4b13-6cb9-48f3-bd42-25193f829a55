# vim:ff=unix syn=python ts=4 sw=4 ai expandtab

{% set cwd = salt['file.normpath'](salt['environ.get']('PWD', salt['config.get']('set_home_directory'))) %}

keystore:
    version: 'latest'
    "debug_mode": 'yes'
    pyenv_path: {{ salt['config.get']('set_pyenv_directory', "/opt/keystore/libexec/pyenv") }}
    "log_level": "debug"
    "uwsgi_socket": '127.0.0.1:8002'
    nginx_port: '8000'
    "newrelic":
        "use_newrelic": False
        "name": "Keystore (Dev-GP2)"
        "license": "984dc8e32ca14d27ff9a9a7241081badb51aa23d"
    "dynatrace":
        "use_dynatrace": True
    "db":
        "user": "app_keystore"
        "password": "pass1234"
        # "cluster": "d-gp2-dbpgkeystore-1.imovetv.com"  # comment out if no cluster
        "connection_age": 10  # suggest 600 for production
        "port": 5432
        # don't change the name of the database, used here to ensure the database exists
        "name": "keystore"
        # current options are 'direct', 'pgbouncer', 'pgpool2', direct is derived looking at the hosts
        "use_connection": "direct"
        "migration_server": "{{ salt['grains.get']('fqdn') }}" # needs to be the FQDN

    "db_connections":
        "pgpool2":
            "host": "/var/run/postgresql"
            "listen_port": 5433
    "message_queue":
        "user": "keystore_rabbit"
        "password": "rabbit_pw"
        "vhost": "keystore"
        "cookie": "5app_keystorefd4b9de-fb43-437a-bc51-60648f6e9b07"  # any arbitrary string
        #"vip": "________"  # uncomment if you have vip in front of cluster
    "require_authentication": 'no'
    "max_range_gen": 5
    "deployment_segment_size": 1800

keystore_postgres_read_only_users: [{"name": "test_r_o_user", 'password': "password1" }]

drm_providers:
    "securemedia_key_url": "http://d-drmsm.movetv.com:8081/kvhls/service"
    "securemedia_band_url": "http://d-drmsm.movetv.com:8083/bdremote/service?cmd=report"
    "securemedia_timeout": 3

    "nagra_url": "http://drmk-beta.echodata.tv:8180/cks-ws-keyAndSignalization/key"
    "nagra_timeout": 5

    "playready_url": "http://d-cg17-playready.d.movetv.com/"  # is a vip
    "playready_timeout": 2

    # the widevine server is found looking at the minions, or uses the vip if provided
    "widevine_vip": "d-cg17-widevine.movetv.com"
    "widevine_timeout": 2

    # the fairplay server is found looking at the minions, or uses the vip if provided
    "fairplay_vip": "d-cg17-fairplay.movetv.com"
    "fairplay_timeout": 2

    # primetime
    "primetime_timout": 2
    "primetime_policy_id": "SLING-TVI-LINEAR-QA"
    "comcast_password": "3GRWXMXANKPZ26N72MMW"

    slingdrm_url: "http://d-gp2-slingdrm-1.imovetv.com:8080"
    slingdrm_timeout: 2

"keystore_varnish": {  # these settings are for the varnish fronting the keystore
    "start_on_boot": "yes",
    "max_open_files": 131072,
    "max_mem_lock": 82000,
    "listen_port": 80,
    "admin_host_and_port": "localhost:6082",
    "vcl": "/etc/varnish/ks-cache.vcl",
    "secret": "/etc/varnish/secret",
    "http_range_support": "on",
    "storage": "malloc,{{ (grains['mem_total'] * 0.5) | int}}m",  # calc and use 50% of memory on box
    # below are the settings for the default.vcl fie
    "purge": [ "localhost", "10.0.0.0/8", "**********/20" ],
    "probe_vars": {
        "interval": "15s",
        "timeout": "2s",
        "window": 7,
        "threshold": 4,
        "url": "/key/version"
    }
}
