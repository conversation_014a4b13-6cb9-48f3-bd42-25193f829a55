# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
.cache
.vagrant
bootstrap-salt.sh
local_salt/minion
local_salt/grains
.pylintrc
pylint
manage
vgr
*.o
*.d
*.swp
*.swo
*~
build/
.waf-*
.waf3-*
.lock-*
pyenv
.sdists-pyenv
db.sqlite3
celerybeat-schedule
pysrc/static_files

tags
cscope.out
cscope.files
.ropeproject/
@PREFIX@

# PyCharm, Visual Studio, MacOS, etc.
.idea
vs_*
*.sln
.DS_Store
*.log
.vscode
