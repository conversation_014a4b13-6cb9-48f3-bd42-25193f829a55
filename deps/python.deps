setuptools>=36

# async task support, also requires pytz, kombu, billiard, amqp, anyjson
celery==4.4.7


# website for monitoring celery, pulls in:
# celery, tornado, babel, futures, pytz, certifi, backbports.ssl-match-hostname, singledispatch, backports-abc
flower==0.9.7

# needed for beat scheduler syncing/locking
pika==1.3.2

# for monitoring
oneagent-sdk
autodynatrace


# make python more thread-like, also pulls greenlet
gevent==21.1.2

# reading config files, pulls in 'six'
configobj==5.0.9

# Extend the system to be like python 3
flufl.enum==6.1.0

# crowd authentication, pulls in requests and lxml
Crowd==2.0.1

# this is needed for python3 compatability with this version of django
six==1.12.0
django-utils-six

# django extensions, also requires six
Django==2.2.5
#django==3.2
django-extensions==1.7.9  # only used when loading old migrations, needs < 1.8.0
#django-extensions==3.2.3
django-jsonfield==1.2.0    # don't update this or it will kill postgres!!!
#django-jsonfield-backport==1.0.5
django-grappelli==2.13.1
#django-grappelli==4.0.1
django-celery-results==1.1.2
#django-celery-results==2.5.1
#psycopg==3.1.19
psycopg2==2.8.6

uWSGI==2.0.18
django-uwsgi==0.2.2
#django-import-export==2.4.0

# encryption support
protobuf==3.20.0 # for widevine, google's protocol buffer
pycryptodome==3.20.0

# for testing the json schema
jsonschema==3.0.2
attrs==19.1.0
pyrsistent==0.15.4
#importlib-metadata
zipp==0.6.0
more-itertools==7.2.0

# for monitoring, don't bother with version, just use newest in .sdists
#newrelic==*********
# for generating the pssh for playready (default xml library won't cut it)
lxml==5.2.2

#testing
mock

#database migrations
sqlparse==0.5.0

pytz==2024.2

# web request support
suds-py3==*******
urllib3==2.2.3
idna==2.10
chardet==3.0.4
certifi==2022.12.7

requests==2.31.0


