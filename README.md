<!--
vim:ff=unix ts=4 tw=120 ai
-->
# Keystore

Keystore manages DRM keys for multiple encryption vendors, currently:

* SecureMedia
* Nagra
* PlayReady
* Widevine
* FairPlay

allowing content to be "simlucrypted".


# Model Layout<a name="model_layout"/></a>
The models for the keystore are laid out as:

![Keystore Models](docs/keystore_model.svg)

The basic idea of how the keystore works is:

* A *key* is used to encrypt a range of a stream.  This information is stored in a `KeyUsage` object, which is created
automatically when the keystore is creating a key.  A single `Key` can potentially be used by multiple `KeyUsage`
objects.
* When creating a `Key` the keystore will rely on the settings in a `KeyConfig` object to tell it what settings to use
when generating the key.  For example, how big is the *range* for a key, which DRM providers it needs to create
information about for the key, and so on.
* When someone requests the creation of a key the need to supply a `KeyConfig` name, and potentially a stream guid and
content-id.  The keystore will then generate a key for the stream, as well as a `KeyUsage` object to associate the key
with the stream.  What is returned is actually a combination of information from the `Key` and `KeyUsage` objects, and
is object just referred to as *the key*.



# External Dependencies
An external sql database supported by django (PostgreSQL, MySQL, SQLite, Oracle, etc).  Ideally the keystore uses
postgres for normal use, and sqlite for its unit tests.  It also requires [RabbitMQ](http://www.rabbitmq.com) for task
management with [Celery](http://www.celeryproject.org).  While it might seem silly to use RabbitMQ, the main reason it
is used it for Key cleanup, and coordination between multiple keystore instances.



# Installation
The Keystore project is built using the [GNU Make](https://www.gnu.org/software/make/) build system. 
The initial configuration is done using Salt, which must be installed and run before running Make.
Then, the configure, build, install and packaging is done using the `make` command, which is included with the project.

- install Salt. You can use `./salt_me.sh` to download and install the latest version.
- run Salt (masterless) to create your Python3 virtual environment using `make configure`. 

If you wish to run Salt commands manually, use the local configuration directory.
Your command line should be something like: 

`sudo salt-call --config-dir=local_salt state.apply keystore.provision`.

### Configure the environment
Anytime `make` is run, it will use a [virtualenv](http://www.virtualenv.org/) "python virtual environment"
located in the `./pyenv` directory.

Help / List commands
> `make` or `make help`

Configure
> `make configure`

Build
> `make build`

Install
> `make install`

Run the unit tests

> `make check`

Create the binary distribution

> `make debpkg`

Clear the build environment

> `make clean`

NOTE: Any `make` command will run the previous commands if needed, i.e. running `make install`
will also run `make configure` and `make build` if they have not already been run.

# Editing the Code
The code is formatted to try and be [PEP8](http://www.python.org/dev/peps/pep-0008/) compliant.  Code formatting is
verified using [pylint](http://www.pylint.org/).  To verify the python code use:
> `make pylint`

which will check all the python source files.  The settings used by `pylint` are stored in the file
`pylintrc`.

Please make sure to verify your code because it is part of the build process in Jenkins.  **If you get pylint
warnings/errors, the CI build will fail.**


# Running

## Activete your Python virtual environment

Before attempting any of the `python` commands, you must be using the Python3 virtual environment created
by the `make configure` script.

`. pyenv/bin/activate`

After running make configure, you can run django management commonands directly, like:

`./manage help`

## Selecting Database
The keystore is currently setup to only support sqlite, and postgresql.  To select which database you want,
you need to edit the file `keystore.conf` and define the database section.  When using postgresql, set it up as follows:

```
[database]
engine = postgresql
name = keystore      <--- name of the database
user = keystore_user <--- use your own value
password = pass      <--- use your own value
host =               <--- left blank defaults to localhost
port =               <--- left blank uses postgresql default
```

The user is responsible for making sure **there is a database with the same name as in `keystore.conf` inside postgres**, and the
corresponding user/password has access to it.  If you want to use sqlite, just change the `engine` field, the other
ones don't matter then.  Note that when running the [unit tests](#unit_tests), it will always use sqlite (intentional).

## Creating DB Tables
You need to make sure your database environment has the proper tables in it.  To do this, you can run the command from
the top level of your virtual environment:
> `./manage migrate`

It will most likely ask you create a super-user for the database.  Please create one as you will need it to access the
admin site for the keystore.  Note that it uses the `keystore.conf` file to determine which database to use (sqlite, or
postgres), and where it is located.


## Starting Everything Manually
### Keystore (Development)
One reason to use the development form of the keystore is that once it is running, if you change any of the python
files it will automatically be detected, and django will restart the keystore for you.  To startup the keystore use the
following at the top level of your git checkout:
> `./manage runserver 0.0.0.0:8001`

Validate that things are running correctly by hitting the admin website <http://localhost:8001/admin> (*Note* the
default runserver port of 8000 is served by nginx).

### Celery
The keystore uses the 3rd party package `celery` to do periodic tasks in the keystore, and coordination between
multiple keystores.  Right now the periodic tasks are:

* the expiration and removal of stale keys and streams
* the creation of pre-cached keys
* the generation of missing metadata (failed keys)

The settings for keystore tasks go in `pysrc/keystore_app/tasks.py`.  Celery consists of 2 main programs, workers (that
process periodic tasks), and the beat (who generates the periodic tasks according to a schedule).  To startup the
celery *worker* process use:
> `celery worker --app=keystore_srv.celerysetup --loglevel=INFO`

To start the celery *beat* process use:
> `celery beat --app=pysrc.keystore_srv.celerysetup --loglevel=WARNING --schedule=pyenv/var/run/celerybeat-schedule`


## Starting Everything using Make/Salt
The keystore has several make commands that use Salt to setup a full dev environment
and get the keystore running.  To do this, right after your `git` checkout you can use:

> `make dev_start`

You should now have the keystore running on your development box, using a `postgresql` database.  You will need to
create any `KeyConfig`s and providers yourself.  To shut it all down, use:
> `make dev_stop`

There are a few caveats to get this to run correctly:

1. You need to be on linux (recommend Ubuntu-16.04).
2. You need to have passwordless sudo setup.
3. Changes done to the source won't instantly be recognized (like when using `./manage runserver`), so you'll need to shut it
down and start it back up for changes to be recognized.


# Keystore API
The published API for the keystore is viewable using <http://localhost:8000/key_docs/api/>.  Many of the web service
calls return json, which have very specific requirements.  The schemas for the various json can be seen at
<http://localhost:8000/key_docs/schemas/>.  Links to this documentation is also provided on the main *admin* page in
the *Documentaion/Support* section.  Below are examples of using some of the more common API calls.


## Authentication
A new addition to the keystore is the requirement that many of the calls require the user to be authenticated.
Requiring authentication is controlled in the Keystore's config file.  Look for the entry `require_auth` and set it how
you want.  If authentication is turned on, to authenticate, use:
> `curl http://d-gp2-ks-1.dev.movenetworks.com/key/login?username=______&password=_______`

Just sub in your username and password.  Currently the keystore uses crowd for authentication, as well as any custom
users entered into the database.  This authenticates the user, and their current `Session`.  All requests should then
use this same Session, and they will pass authentication.   Example Python code on how to do this is:

```
import requests
sess = requests.session()
sess.headers.update({"Content-Type": "application/json", "Accept": "application/json"})
params = {"username": "john_doe", "password": "hidden"}
rsp = sess.get("http://d-gp2-ks-1.dev.movenetworks.com/key/login", params=params)
if rsp.ok:
	print "I'm authenticated"
	rsp = sess.get("http://d-gp2-ks-1.dev.movenetworks.com/key/block/live/............")
```

For the remaining examples, it assumes the user has already been authenticated.


## Generating Keys ('get' endpoint - deprecated)
**This endpoint is deprecated, clients should migrate to using the *block* endpoint.**<br>
The service is now running, and you should be able to hit the keystore, and generate a key. The following is an example.
> `curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/1`

This url can be rewritten to show it's key components:
> `curl http://localhost:8000/key/get/<KeyConfig name>/<Content ID>/<Stream ID>/<Segment Start>[-<Segment End>]`

There are 5 main elements to this url:
1. `KeyConfig Name` - the name of an existing KeyConfig in the keystore.  This is used if the stream doesn't exist.  If
the stream doesn't exist, and the KeyConfig doesn't exist, you'll get an error back.
2. `Content ID` - the ID for the content to send to the provider.  In the case of *live* content for Secure Media, this
is the band.
3. `Stream ID` - this is a 32-byte hex value representing the UUID for a stream.
4. `Segment Start` - the segment number of the stream you want the key for.
5. `Segment End` - an optional part of the URL that is the last segment you want the key for. In the event that the
start and end values span multiple segments, you will get multiple keys back.


## Generating Keys ('block' endpoint)
Issues were seen in production where the 'get' endpoint above was having issues.  The problem is that 'get' key
requests aren't easily cacheable by Varnish, so multiple dynapacks hitting the keystore for different segments in the
same stream (thus the same key) were making it all the way to the keystore (and database), when in reality they all
resulted in the same response.  The result was the creation of the 'block' endpoint.  This endpoint behaves the same as
the 'get' endpoint, but instead of sending in a segment number, you give a *block* number.  A block is defined as a
range of segments, the size of which is taken from the corresponding `KeyConfig`.  A sample URL is:
> `curl http://d-gp2-ks-1.dev.movenetworks.com/key/block/all_providers/readme/0123456789abcdef0123456789abcdef/1`

This url can be rewritten to show it's key components:
> `curl http://localhost:8000/key/block/<KeyConfig name>/<Content ID>/<Stream ID>/<Block Number>`

There are 4 main elements to this url:
1. `KeyConfig Name` - the name of an existing KeyConfig in the keystore.  This is used if the stream doesn't exist.  If
the stream doesn't exist, and the KeyConfig doesn't exist, you'll get an error back.
2. `Content ID` - the ID for the content to send to the provider.  In the case of *live* content for Secure Media, this
is the band.
3. `Stream ID` - this is a 32-byte hex value representing the UUID for a stream.
4. `Block Number` - the number of the block to get keys for.  Block numbers start at 1.  **Note: you can't request
block ranges.**

Thus in the old format there was a *huge* number of URL variants for the same key, and thus the same results.  For
example (assuming a block size of 1800), the following URLs are generate the same response:

```
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/1
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/2
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/10
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/100
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/1-5
curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/100-500
...
```

Using the new *block* endpoint these all collapse down to a single (and thus cacheable) URL:
> `curl http://d-gp2-ks-1.dev.movenetworks.com/key/block/all_providers/readme/0123456789abcdef0123456789abcdef/1`



### Sample Output
Using the above sample curl commands, you should receive back something like:
> `{"keys": [{"key": "074b6523bf5ef52d64b117c79fc3c07a", "end_seg": 49, "algorithm": "AES_128_CBC", "providers": [{"mkid": "dv-lygxh", "type": "securemedia", "name": "sm_live", "pcw": "AWR2LWx5Z3hoXU2sXETyMm3ryWUR-o1wHp72vzSAxw8CZhflxmpROBHtdi887O5qA8A"}, {"drm_sys_id": "adb41c24-2dbf-4a6d-958b-4457c0d27b95", "key_uri": "http://www.nagra.com/key=readme&prm=eyJjb250ZW50SWQiOiJuYWdfbmFtZSIsImtleUlkIjoiYmY5NGEyZWMtNDNjOC00NjhkLThhMjUtNmUyMDg1YzVlM2M4In0=", "type": "nagra", "name": "nag_live", "drm_name": "PRM"}], "key_id": "bf94a2ec-43c8-468d-8a25-6e2085c5e3c8", "expires": "2014-02-11 12:34:34", "start_seg": 0}], "stream_id": "01234567-89ab-cdef-0123-456789abcdef", "media_id": "devenc009", "media_type": "live", "encryption_block_size": 1800}`

Or reformatted:

```
{
    "keys": [
        {
            "algorithm": "AES_128_CBC",
            "end_seg": 49,
            "expires": "2014-02-11 12:34:34",
            "key": "074b6523bf5ef52d64b117c79fc3c07a",
            "key_id": "bf94a2ec-43c8-468d-8a25-6e2085c5e3c8",
            "providers": [
                {
                    "mkid": "dv-lygxh",
                    "name": "sm_live",
                    "pcw": "AWR2LWx5Z3hoXU2sXETyMm3ryWUR-o1wHp72vzSAxw8CZhflxmpROBHtdi887O5qA8A",
                    "type": "securemedia"
                },
                {
                    "drm_name": "PRM",
                    "drm_sys_id": "adb41c24-2dbf-4a6d-958b-4457c0d27b95",
                    "key_uri": "http://www.nagra.com/key=readme&prm=eyJjb250ZW50SWQiOiJuYWdfbmFtZSIsImtleUlkIjoiYmY5NGEyZWMtNDNjOC00NjhkLThhMjUtNmUyMDg1YzVlM2M4In0=",
                    "name": "nag_live",
                    "type": "nagra"
                }
            ],
            "start_seg": 0
        }
    ],
    "stream_id": "01234567-89ab-cdef-0123-456789abcdef"
    "media_id": "devenc009",
    "media_type": "live",
    "encryption_block_size": 1800
}
```

Notice the square brackets (a list) inside the `key` label.  There is only one key in this example, but in the event
that the request segment range spanned multiple keys, there would be multiple keys inside this list.


### Generating Time-based Rotation Keys
When creating a `KeyConfig` you can specify that it rotates a key based on the time of day.  To generate a key that
uses such a `KeyConfig` you need to supply 2 additional query args on the GET request:

1. `create_time` - This is a Unix timestamp of when the stream was created.  The value must be in seconds, with no
decimals.
2. `duration` - How many *milliseconds* long are segments in this stream.  The value can't have decimals.

Once these values get set for a stream, they can't be changed.  The rest of the query URL is the same as above.  An
example query would be:
> `curl http://d-gp2-ks-1.dev.movenetworks.com/key/get/all_providers/readme/0123456789abcdef0123456789abcdef/400?create_time=134353634&duration=2048`

Because this `KeyConfig` is time-based, instead of having `encryption_block_size` in the returned json, it has
`rotation_time` which indicates what time during the day the key rotation occurs (e.g., 03:00:00 is 3am).


## Removing Keys
Normally keys are removed by the celery task once an hour if the key has expired.  There may be times when you want to
explicitly remove a key from the keystore.  Either it has an infinite lifetime so celery won't remove it, or we want to
remove it before it's normal expiration.  This can be done using the following command:
> `curl http://localhost/key/remove/<key_id>`

Where `key_id` is the UUID of the key (no dashes) to be removed.  The response will either be `{"status": "success"}`,
or `{"error": "No such key"}`.


## Checking Provider Connections
There are several ways you can check provider connections with the key store.  The options are:

1. Using the admin webpage - you can then check individual providers, which uses either the default url, or the
providers custom url.
2. Using the `check` url.  Currently you can check:
	- Secure Media servers, which always uses the default url.
	- Nagra servers, which always uses the default url.
	- PlayReady servers, which always use the default url.
	- entire `KeyConfig`s (all its associated providers).  **This is the only way to check providers with custom URLs
	with a simple web request.**
3. Using the status endpoint which does a check on every single provider *being used by a `KeyConfig`* in keystore, and
returns a json block with the result for `KeyConfig`, with nested entries for each provider used by that KeyConfig.

The structure of the url requests are:
> `curl http://localhost/check/<provider_name>/<timeout>`<br>
> `curl http://localhost/check/<config_name>/<timeout>`<br>
> `curl http://localhost/status`

The main elements of these urls are:

1. `provider_name` - the name of the provider (`securemedia`, `nagra`, or `playready`).
2. `config_name` - name of the Key Config you want to check.
3. `timeout` - an optional timeout for the amount of time to wait (in seconds) for a response from the server.  Useful
if you suspect a slow network connection.  If not supplied, it will use the default values from the `keystore.conf`
file.  For checking a KeyConfig, it will default to 10.


### Sample Output
The output from testing an individual provider looks like:
> `{"status": "OK", "healthy": true}`

In the event that the status isn't `OK`, or healthy is `false`, there is also optional `message` field that could
appear describing why the connection check failed.  For example:
> `{"status": "FAILED", "healthy": false, "message": "Supplied url is not a Secure Media server"}`

The output when checking a Key Config has a similar structure.  It contains a `provider` list, where each item in the
list is the name of the provider, followed by a dictionary containing the results from that provider.  One additional
element is the `url` used for the connection check.  For example, a Key Config with both a Secure Media provider (named
`sm1`), and a Nagra provider (named `nag1`) is:

```
{
    "live": false,
    "rotation": 100,
    "providers": [
        {
            "sm1": {
                "status": "OK",
                "healthy": true,
                "url": "http://smapp001.dev.movenetworks.com:8081/kvhls/service"
            }
        },
        {
            "nag1": {
                "status": "OK",
                "healthy": true,
                "url": "http://200.20.100.110:8180/cks-ws-keyAndSignalization/key"
            }
        }
    ]
}
```

Possible errors include checking a `KeyConfig` that doesn't exist.  If you check a `KeyConfig` that has no providers,
it will send back an empty 'providers' list, but also contain a message that there are no providers associated with
that Key Config.  Examples of these are:
> `{"error": "No Key Config found"}`<br>
> `{"message": "No providers for this key config", "providers": []}`


## Getting Raw Keys
The keystore does allow you to access the raw/secret keys.  **This is mainly for dynamux using the InternalProvider**.  
There are 2 ways you can request the raw value for a key:

1. Asking for the value based on the Key's ID
> `curl http://localhost/key/raw/<key_id>`

2. Asking for the value based on a stream ID and segment that use the key in question.
> `curl http://localhost/key/raw/<stream_id>/<segment_num>`

In either case, if the calls works the body of the returned webpage will be 16 bytes, the secret value for the key.  
Note that since these 16 bytes could be anything, they may not be displayable ASCII values.  In the event the requested
key doesn't exist, you'll get back `{"error": "No Key found"}`.


## Getting a list of KeyConfigs
The keystore also allows you to obtain a list of names of all the KeyConfigs in the system.  This is useful for the
dynamux admin system.  To obtain the list use (note the slash on the end):
> `curl http://localhost/keyconfigs/`

The return value is json that contains the list (named `keyconfigs`) of all the KeyConfig names.  Sample out is:
> `{"keyconfigs": ["foo", "bar", "pr1"]}`


## Getting Key Info
The keystore also allows keys to be served without requiring the generation of a key.  This is similar to the `block`
endpoint in what it returns, the primary difference is that this endpoint just returns information on *existing*, it
won't generate a new key if no match is found (will return a 404 then).
> `curl http://localhost/key/info/<key_id>`

This endpoint is used primary by the key-provider servers (PlayReady, Widevine, FairPlay, etc).


### Sample Output
An example of using this endpoint is:
> `curl http://localhost/key/info/ffa89db5-d4ba-48fa-8f63-bbf2f871f758`


The formatted returned value looks like:
```
{
    "algorithm": "AES_128_CBC",
    "end_seg": 21600,
    "providers": [
        {
            "pssh": "2e0300000100010024033c00570052004d00480045004100440045005200200078006d006c006e0073003d00220068007400740070003a002f002f0073006300680065006d00610073002e006d006900630072006f0073006f00660074002e0063006f006d002f00440052004d002f0032003000300037002f00300033002f0050006c00610079005200650061006400790048006500610064006500720022002000760065007200730069006f006e003d00220034002e0030002e0030002e00300022003e003c0044004100540041003e003c00500052004f00540045004300540049004e0046004f003e003c004b00450059004c0045004e003e00310036003c002f004b00450059004c0045004e003e003c0041004c004700490044003e004100450053004300540052003c002f0041004c004700490044003e003c002f00500052004f00540045004300540049004e0046004f003e003c004c0041005f00550052004c003e0068007400740070003a002f002f0064002d0063006700310037002d0070006c0061007900720065006100640079002e0064002e006d006f0076006500740076002e0063006f006d002f0070006c0061007900720065006100640079002f007200690067006800740073006d0061006e0061006700650072002e00610073006d0078003c002f004c0041005f00550052004c003e003c004c00550049005f00550052004c003e0068007400740070003a002f002f0064002d0063006700310037002d0070006c0061007900720065006100640079002e0064002e006d006f0076006500740076002e0063006f006d002f006c006f00670069006e002e0061007300700078003c002f004c00550049005f00550052004c003e003c004b00490044003e0074005a0032006f002f003700720055002b006b006900500059003700760079002b00480048003300570041003d003d003c002f004b00490044003e003c0043004800450043004b00530055004d003e004300320061006c0078004400530051005200430049003d003c002f0043004800450043004b00530055004d003e003c002f0044004100540041003e003c002f00570052004d004800450041004400450052003e00",
            "type": "playready",
            "name": "pr_default",
            "scheme_id_uri": "urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95"
        },
        {
            "key_uri": "http://localhost/hlskey?pcw=AWR2b2w4a3FioWOjxSnCTGG-RQpR81Xq4g1tvGpqQu3W7ni5ZxMhq47Km9A5z1vhPAA",
            "type": "securemedia",
            "name": "sm_default",
            "pcw": "AWR2b2w4a3FioWOjxSnCTGG-RQpR81Xq4g1tvGpqQu3W7ni5ZxMhq47Km9A5z1vhPAA",
            "mkid": "dvol8kqb"
        },
        {
            "key_uri": "http://d-gp2-ks-2.dev.movenetworks.com/key/raw/ffa89db5d4ba48fa8f63bbf2f871f758",
            "pssh": "",
            "type": "internal",
            "name": "internal_default",
            "scheme_id_uri": "urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b"
        },
        {
            "pssh": "0801122066666138396462356434626134386661386636336262663266383731663735381a07736c696e6774762203746e742a0553445f4844",
            "proxy_url": "http://d-cg17-widevine.d.movetv.com:8080/widevine/proxy",
            "type": "widevine",
            "name": "wv_default",
            "scheme_id_uri": "urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"
        },
        {
            "name": "fp_default",
            "key_uri": "skd://d-cg17-fairplay.d.movetv.com:8080/fairplay/ffa89db5d4ba48fa8f63bbf2f871f758",
            "key_format": "com.apple.streamingkeydelivery",
            "key_format_version": 1,
            "type": "fairplay",
            "cert_uri": "http://d-cg17-fairplay.d.movetv.com:8080/fairplay/certificate"
        }
    ],
    "expires": "2016-11-21 19:18:25",
    "start_seg": 14401,
    "key": "47350739f8e15e489e500785b1438352",
    "has_all_meta": true,
    "key_id": "ffa89db5d4ba48fa8f63bbf2f871f758"
}
```


## Getting keys for the SlingBox
The slingbox team wants to use the keystore as a multi-DRM system, so the idea of different providers is abstracted
away from them.  As such, they have their own endpoint they can use to get/create keys.  Keys created using this
mechanism don't have an associated `KeyUsage` object, it is just a key that lives for the designated amount of time.  
The endpoint is `/key/slingbox`, and is acutally a POST, where the client needs to indicate whether they want an
existing key (by supplying a `key_id` field), or create a new key (by supplying a `keyconfig` name field).


### Sample Output
An example of using this endpoing is:
> `curl -X POST -d '{"keyconfig": "segment_live_config"}' -H "Content-Type: application/json"  http://localhost/key/slingbox`

The formatted returned value looks like:

```
{
    "key_id": "85956e92a1e0424783426ed1930150b5",
    "expires": "2015-05-20 15:07:45",
    "key": "ab72d770ffe498e35b5e0c5c5fc43e75",
    "providers": [
        {
            "key_uri": "http://localhost/hlskey?pcw=AWR2bGQ1bWhrSegWTmSPaWUKhkZy60J01uqySnxWCAz3iUVa1c-Byhi4Riw6wXa5KgA",
            "mkid": "dvld5mhk",
            "type": "securemedia",
            "name": "sm_default",
            "pcw": "AWR2bGQ1bWhrSegWTmSPaWUKhkZy60J01uqySnxWCAz3iUVa1c-Byhi4Riw6wXa5KgA"
        },
        {
            "key_uri": "http://ubuntu-14-04/key/raw/85956e92a1e0424783426ed1930150b5",
            "type": "internal",
            "name": "internal_default"
        },
        {
            "drm_sys_id": "adb41c24-2dbf-4a6d-958b-4457c0d27b95",
            "key_uri": "http://www.nagra.com/key=slingbox&prm=eyJjb250ZW50SWQiOiJzbGluZ2JveCIsImtleUlkIjoiODU5NTZlOTItYTFlMC00MjQ3LTgzNDItNmVkMTkzMDE1MGI1In0",
            "type": "nagra",
            "name": "ng_default",
            "drm_name": "PRM"
        }
    ]
}
```

## Viewing Json Schema
The keystore publishes all the json schemas that it sends back.  They can be viewed by pointing your browser (best not
to use curl) to <http://localhost/key_docs/schemas>.  This page should give a list of all the json schemas.  A
link to this page an also be found on the Keystore's admin page.  **Note that if nothing is listed you need to collect
static files by running:**
> `python pysrc/manage.py collectstatic --link --noinput`


## Generating keys for the RSDVR
Keys for the RSDVR behave slightly different than regular live/vod keys.  RSDVR keys are per-user, and as such it has
it's own special endpoint to generate them.
> `curl http://localhost/key/rsdvr/<keyconfig_name>/<user_guid>`

There are a couple of important differences when using the RSDVR endpoint compared to regular key generation:
1. The `content_id` is set to be `rsdvr`.
2. The encryption block size is hard-coded to be 85000 segments.
3. If the key has expired, a new key will be generated when requested.


# Installing from the Debian Package
The keystore can be deployed using a Debian package.  To create the Debian package use:
> `make debpkg`

From the root of your git checkout.  This will create a Debian package and place it in the same directory containing
your git checkout.  This package can then be installed on any Debian linux system.  A few things to note about setting
things up after installing the package:

1. The package has dependencies on `nginx`, `ntp`, and `python3`.
2. The install will be placed into `/opt/keystore`
3. The install will create a file `/opt/keystore/etc/keystore.conf.exmaple` as an example config file, as well as default config files for a local-only setup. To change the keystore configuration:
	1. Edit the config file `/etc/keystore.conf` with your information.
	2. If you are using postreSQL, make sure you have database with the name specified in `/etc/keystore.conf` (default: "keystore").  The username and password for that database should go into the above config file.
	3. Restart the keystore using `sudo start keystore` or `sudo service keystore start`
4. If you uninstall the keystore, the `keystore.conf` file is left behind, such that if it gets reinstalled in the future, it will start right away using the old config file unless replaced on installation.
5. The default port is the standard web server port of 80 when installed from the package.

## Logging into the Admin Website
The keystore admin site uses *crowd* for authentication.  So assuming you have access to the *dynamux* application
in crowd, you should be allowed to access the admin site.  To log into the admin site just direct your browser to
<http://d-gp2-ks-1.dev.movenetworks.com/admin> and enter your standard network username and password (e.g., the same
one you use to access jira).



# Unit Tests <a name="unit_tests"/></a>
When unit tests are run on the keystore, a temporary database is used inside of sqlite.  It starts empty, and is
cleaned after each unit test.  This is all handled by the unit test framework.

To run the unit tests, from the top level directory use:
> `make check`

This will run all the unit tests for the keystore.  If you want to just a specific subset of the unit tests the format
is:
> `python pysrc/manage.py test keystore_app.tests.<class_name>.<method_name>`

If you don't supply a method name, it will run all the units tests in the given class.  An example of calling a single
test function is:
> `python pysrc/manage.py test keystore_app.tests.KeyUsageTests.test_disjoint_key_requests`

A recent addition to the test suite is the verification of the json being returned by the keystore.  The json schema is
defined in the file `pysrc/keystore_app/schemas/encryption.schema.json`.  This file describes what fields are allowed
in the returned json, along with specific values for certain files.  This read in during the unit tests, and the json
returned by the keystore is compared against the schema to make sure it matches.  If the json doesn't match, the unit
test(s) will fail with a `ValidationError`.
